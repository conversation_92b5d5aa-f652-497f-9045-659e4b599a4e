/// <reference path="../pb_data/types.d.ts" />

// Helper function to generate invoice PDF
async function generateInvoicePdf({totalAmount, invoicePercentage, record}) {
    // This is a placeholder function - implement actual PDF generation here
    // In a real implementation, you would use a PDF library like PDFKit
    // and generate a proper invoice with company details, line items, etc.
    
    const invoiceAmount = (totalAmount * (invoicePercentage / 100));
    const invoiceData = {
        invoiceNumber: `INV-${record.id}`,
        date: new Date().toLocaleDateString('en-GB'),
        companyName: 'Africa Expo Property & Lifestyle Ltd',
        companyAddress: '78 York Street, London, England, W1H 1DP',
        customerName: record.get('name'),
        customerEmail: record.get('email'),
        amount: invoiceAmount,
        percentage: invoicePercentage,
        totalAmount: totalAmount,
        vatRate: 20, // UK VAT rate
        vatAmount: invoiceAmount * 0.20
    };
    
    // For now return dummy buffer - replace with actual PDF generation
    const dummyPdfBuffer = Buffer.from(JSON.stringify(invoiceData));
    return dummyPdfBuffer;
}



//hooks for sponsors
onRecordAfterUpdateSuccess((e) => {

    const record = e.record;
    // Helper functions
    const formatDate = (date) => {
        return new Date(date).toLocaleDateString('en-GB', {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
        });
    };
    

    const getPaymentDetails = (totalAmount) => {
        const downPayment = totalAmount * 0.25;
        const remainingAmount = totalAmount * 0.75;
        const orderDate = new Date(record.get('order_date'));
        const dueDate = new Date(orderDate);
        dueDate.setDate(dueDate.getDate() + 30);

        return {
            totalAmount,
            downPayment,
            remainingAmount,
            dueDate: formatDate(dueDate)
        };
    };

    // Handle different update scenarios
    const handleEmails = async () => {
        const currentDate = formatDate(new Date());
        const totalAmount = parseFloat(record.get('total_fee'));
        const { downPayment, remainingAmount, dueDate } = getPaymentDetails(totalAmount);

        // Parse sponsorship options
        const sponsorshipOptions = JSON.parse(record.get('sponsorship_menu_options'));
        const sponsorshipList = sponsorshipOptions.map(option => 
            `<li>${option}</li>`
        ).join('');

        // Base email configuration
        const baseEmailConfig = {
            from: {
                address: $app.settings().meta.senderAddress,
                name: 'Africa Expo Property & Lifestyle 2025',
            },
            to: [{
                address: record.get('email'),
                name: record.get('name')
            }]
        };

        try {
            // If down payment is updated to true
            if ((record.get('down_payment') && record.get('email_template') === 'down-payment')) {
                const paymentConfirmation = new MailerMessage({
                    ...baseEmailConfig,
                    subject: 'Sponsorship Down Payment Received - Africa Expo 2025',
                    html: `
                    <!DOCTYPE html>
                        <html lang="en">
                        <head>
                            <meta charset="UTF-8">
                            <meta name="viewport" content="width=device-width, initial-scale=1.0">
                            <title>Sponsorship Payment Confirmation</title>
                            <style>
                                * {
                                    margin: 0;
                                    padding: 0;
                                    box-sizing: border-box;
                                    font-family: Arial, sans-serif;
                                }

                                body {
                                    background-color: #f5f5f5;
                                    padding: 20px;
                                }

                                .container {
                                    max-width: 600px;
                                    margin: 0 auto;
                                    background: linear-gradient(to bottom right, #ffffff, #f8f9fa);
                                    border-radius: 12px;
                                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                    overflow: hidden;
                                }

                                .header {
                                    background: linear-gradient(135deg, #4f46e5, #7c3aed);
                                    padding: 30px;
                                    margin-bottom: 20px;
                                }

                                h2 {
                                    color: white;
                                    font-size: 24px;
                                    margin: 0;
                                }

                                .content {
                                    padding: 0 30px 30px;
                                }

                                h3 {
                                    color: #333;
                                    margin-top: 0;
                                    font-size: 20px;
                                    margin-bottom: 15px;
                                }

                                p {
                                    color: #444;
                                    line-height: 1.6;
                                    margin-bottom: 15px;
                                }

                                .section {
                                    padding: 25px;
                                    margin: 20px 0;
                                    border-radius: 8px;
                                    border: 1px solid rgba(0, 0, 0, 0.1);
                                }

                                .payment-details {
                                    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                                }

                                .payment-terms {
                                    background: linear-gradient(135deg, #fff4e6, #ffe8cc);
                                }

                                .next-steps {
                                    background: linear-gradient(135deg, #e6f3ff, #d0e8ff);
                                }

                                .contact-info {
                                    background: linear-gradient(135deg, #f8f8f8, #f0f0f0);
                                }

                                ul, ol {
                                    margin-left: 20px;
                                    margin-bottom: 15px;
                                }

                                li {
                                    color: #444;
                                    line-height: 1.6;
                                    margin-bottom: 10px;
                                }

                                strong {
                                    color: #333;
                                }

                                .footer {
                                    margin-top: 30px;
                                    padding-top: 20px;
                                    border-top: 1px solid #eee;
                                }

                                .footer p {
                                    font-size: 14px;
                                    color: #666;
                                    line-height: 1.6;
                                }

                                a {
                                    color: #4f46e5;
                                    text-decoration: none;
                                }

                                a:hover {
                                    text-decoration: underline;
                                }

                                @media (max-width: 640px) {
                                    .content {
                                        padding: 0 20px 20px;
                                    }

                                    .header {
                                        padding: 25px 20px;
                                    }

                                    .section {
                                        padding: 20px;
                                    }
                                }
                            </style>
                        </head>
                        <body>
                            <div class="container">
                                <div class="header">
                                    <h2>Sponsorship Payment Confirmation</h2>
                                </div>
                                
                                <div class="content">
                                    <p>Dear ${record.get('name')},</p>
                                    
                                    <p>We are delighted to confirm receipt of your down payment for your sponsorship at Africa Expo Property & Lifestyle 2025 - ${record.get('season')}.</p>
                                    
                                    <div class="section payment-details">
                                        <h3>Payment Details</h3>
                                        <p><strong>Sponsorship Package:</strong></p>
                                        <ul>
                                            ${sponsorshipList}
                                        </ul>
                                        <p><strong>Total Amount:</strong> £${totalAmount.toLocaleString()}</p>
                                        <p><strong>Down Payment Made:</strong> £${(totalAmount * 0.25).toLocaleString()} (25%)</p>
                                        <p><strong>Remaining Balance:</strong> £${remainingAmount.toLocaleString()} (75%)</p>
                                        <p><strong>Balance Due Date:</strong> ${dueDate}</p>
                                    </div>

                                    <div class="section payment-terms">
                                        <h3>Important Payment Terms</h3>
                                        <ul>
                                            <li>The remaining balance of £${remainingAmount.toLocaleString()} must be paid on or before ${dueDate}</li>
                                            <li>All fees are inclusive of Value Added Tax(VAT) at the prevailing rate</li>
                                            <li>${record.get('payment_terms')}</li>
                                        </ul>
                                    </div>

                                    <div class="section next-steps">
                                        <h3>Next Steps</h3>
                                        <ol>
                                            <li>Save this confirmation for your records</li>
                                            <li>Schedule the remaining payment before the due date</li>
                                            <li>Our sponsorship team will contact you shortly to discuss:
                                                <ul>
                                                    <li>Logo and branding requirements</li>
                                                    <li>Marketing material specifications</li>
                                                    <li>Technical requirements for your sponsorship assets</li>
                                                    <li>How to make remaining payments</li>
                                                </ul>
                                            </li>
                                        </ol>
                                    </div>

                                    <div class="section contact-info">
                                        <h3>Your Sponsorship Contact</h3>
                                        <p>Your dedicated sponsorship manager will be in touch within 2 business days to begin planning your sponsorship activation.</p>
                                        
                                        <p>For immediate queries:</p>
                                        <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a><br>
                                        Phone: <a href="tel:+************">+44 (0) 20 7692 7003</a></p>
                                    </div>

                                    <div class="footer">
                                        <p>
                                            Best regards,<br>
                                            Africa Expo Property & Lifestyle Ltd.<br>
                                            78 York Street, London, England, W1H 1DP<br>
                                            Date: ${currentDate}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </body>
                        </html>
                `
                });
                
                await $app.newMailClient().send(paymentConfirmation);
                console.log(`Payment confirmation email sent to ${record.get('email')}`);
            }
            
            // If send invoice is updated to true
            if (record.get('send_invoice') && record.get('email_template') === 'bank-details') {
                const invoicePercentage = parseFloat(record.get('invoice_percentage')) || 0;
                const invoicePdfBuffer = await generateInvoicePdf({
                    totalAmount, 
                    invoicePercentage, 
                    record
                });
                const invoiceAttachment = new MailAttachment({
                    filename: `invoice-exhibitor-${record.id}.pdf`,
                    contentType: 'application/pdf',
                    content: invoicePdfBuffer,
                });
                const invoiceEmail = new MailerMessage({
                    ...baseEmailConfig,
                    subject: 'Invoice for your Exhibition Space - Africa Expo 2025',
                    html: `
                    <!DOCTYPE html>
                    <html lang="en">
                    <head>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <title>Invoice</title>
                        <style>
                            * {
                                margin: 0;
                                padding: 0;
                                box-sizing: border-box;
                                font-family: Arial, sans-serif;
                            }
                            body {
                                background-color: #f5f5f5;
                                padding: 20px;
                            }
                            .container {
                                max-width: 600px;
                                margin: 0 auto;
                                background: linear-gradient(to bottom right, #ffffff, #f8f9fa);
                                border-radius: 12px;
                                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                overflow: hidden;
                            }
                            .header {
                                background: linear-gradient(135deg, #4f46e5, #7c3aed);
                                padding: 30px;
                                margin-bottom: 20px;
                            }
                            h2 {
                                color: white;
                                font-size: 24px;
                                margin: 0;
                            }
                            .content {
                                padding: 30px;
                            }
                            p {
                                color: #444;
                                line-height: 1.6;
                                margin-bottom: 15px;
                            }
                            .footer {
                                margin-top: 30px;
                                padding-top: 20px;
                                border-top: 1px solid #eee;
                            }
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <div class="header">
                                <h2>Invoice for Exhibition Space</h2>
                            </div>
                            <div class="content">
                                <p>Dear ${record.get('name')},</p>
                                <p>Please find attached your invoice for ${invoicePercentage}% of your Africa Expo 2025 exhibition space.</p>
                                <p>Payment Details:</p>
                                <ul>
                                    <li>Invoice Amount: £${((totalAmount * invoicePercentage) / 100).toLocaleString()}</li>
                                    <li>Due Date: ${formatDate(new Date())}</li>
                                </ul>
                                <p>If you have any questions about this invoice, please don't hesitate to contact us.</p>
                                <div class="footer">
                                    <p>
                                        Best regards,<br>
                                        Africa Expo Property & Lifestyle Ltd.<br>
                                        78 York Street, London, England, W1H 1DP
                                    </p>
                                </div>
                            </div>
                        </div>
                    </body>
                    </html>
                    `,
                    attachments: [invoiceAttachment],
                });
                await $app.newMailClient().send(invoiceEmail);
                console.log(`Invoice email sent to ${record.get('email')}`);
            }

            // If send_bank_details is updated to true
            if ((record.get('send_bank_details') && record.get('email_template') === 'bank-details')) {
                const bankDetails = new MailerMessage({
                    ...baseEmailConfig,
                    subject: 'Bank Transfer Details - Africa Expo 2025 Sponsorship',
                    html: `
            <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bank Transfer Instructions</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: Arial, sans-serif;
        }

        body {
            background: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header h2 {
            font-size: 20px;
            font-weight: normal;
        }

        .content {
            padding: 30px;
        }

        .section {
            margin-bottom: 30px;
            padding: 25px;
            border-radius: 8px;
        }

        .section h3 {
            color: #1f2937;
            margin-bottom: 20px;
            font-size: 20px;
        }

        .sponsorship-package {
            background: linear-gradient(135deg, #eff6ff, #eef2ff);
            border: 1px solid #dbeafe;
        }

        .bank-details {
            background: linear-gradient(135deg, #f5f3ff, #faf5ff);
            border: 1px solid #e9d5ff;
        }

        .bank-details-inner {
            background: white;
            padding: 20px;
            border-radius: 6px;
        }

        .payment-terms {
            background: linear-gradient(135deg, #ecfdf5, #f0fdf4);
            border: 1px solid #d1fae5;
        }

        .next-steps {
            background: linear-gradient(135deg, #fffbeb, #fef3c7);
            border: 1px solid #fde68a;
        }

        .help-section {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            border: 1px solid #e2e8f0;
        }

        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #4b5563;
            font-size: 14px;
            line-height: 1.6;
        }

        p {
            margin-bottom: 15px;
            line-height: 1.6;
            color: #374151;
        }

        ul, ol {
            margin-left: 20px;
            margin-bottom: 15px;
        }

        li {
            margin-bottom: 10px;
            color: #374151;
        }

        .bank-details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .detail-item {
            margin-bottom: 10px;
        }

        .detail-item strong {
            color: #1f2937;
        }

        a {
            color: #4f46e5;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        @media (max-width: 640px) {
            .container {
                margin: 10px;
            }

            .header {
                padding: 30px 20px;
            }

            .content {
                padding: 20px;
            }

            .section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Bank Transfer Instructions</h1>
            <h2>Africa Expo Property & Lifestyle 2025 Sponsorship</h2>
        </div>

        <div class="content">
            <p>Dear ${record.get('name')},</p>
            
            <p>Thank you for choosing to sponsor Africa Expo Property & Lifestyle 2025 - ${record.get('season')}. 
               Below are your sponsorship details and bank transfer information.</p>

            <div class="section sponsorship-package">
                <h3>Selected Sponsorship Package</h3>
                ${sponsorshipList}
                <p><strong>Total Fee:</strong> £${totalAmount.toLocaleString()}</p>
                <p><strong>Down Payment (25%):</strong> £${downPayment.toLocaleString()}</p>
                <p><strong>Payment Deadline:</strong> ${dueDate}</p>
            </div>

            <div class="section bank-details">
                <h3>Bank Transfer Details</h3>
                <div class="bank-details-inner">
                    <div class="bank-details-grid">
                        
                        <div class="detail-item">
                            <strong>Account Name:</strong> Ghana Property & Lifestyle Expo Ltd
                        </div>
                        <div class="detail-item">
                            <strong>Sort Code:</strong> 20-90-74
                        </div>
                        <div class="detail-item">
                            <strong>Account Number:</strong> ********
                        </div>
                        <div class="detail-item">
                            <strong>IBAN:</strong> GB16 BUKB 2090 7443 8866 03
                        </div>
                        <div class="detail-item">
                            <strong>SWIFT/BIC:</strong> BUKBGB22
                        </div>
                    </div>
                    <div style="margin-top: 20px;">
                        <div class="detail-item">
                            <strong>Address:</strong> 78 York Street, London, England, W1H 1DP
                        </div>
                        <div class="detail-item">
                            <strong>Bank Address:</strong> Clapham Junction Branch. 7 St John's Hill Clapham Junction SW11 1TR
                        </div>
                        <div class="detail-item">
                            <strong>Reference:</strong> SPONSOR-${record.id}
                        </div>
                    </div>
                </div>
            </div>

            <div class="section payment-terms">
                <h3>Payment Terms</h3>
                <ul>
                    <li>Please include reference SPONSOR-${record.id} in your transfer</li>
                    <li>Initial payment (25%): £${downPayment.toLocaleString()}</li>
                    <li>Payment deadline: ${dueDate}</li>
                    <li>Balance payment (75%): Due within 30 days</li>
                    <li>All fees are inclusive of All prices are inclusive of Value Added Tax(VAT) at the prevailing rate.</li>
                </ul>
            </div>

            <div class="section next-steps">
                <h3>Next Steps</h3>
                <ol>
                    <li>Complete the bank transfer using the details above</li>
                    <li>Email the transfer <NAME_EMAIL></li>
                    <li>We will send a payment receipt once the transfer is confirmed</li>
                    <li>Our team will then contact you regarding:
                        <ul>
                            <li>Logo and branding requirements</li>
                            <li>Marketing material specifications</li>
                            <li>Technical specifications for your sponsorship assets</li>
                        </ul>
                    </li>
                </ol>
            </div>

            <div class="section help-section">
                <h3>Need Help?</h3>
                <p>For any questions about the payment process, please contact us:</p>
                <p>
                    Email: <a href="mailto:<EMAIL>"><EMAIL></a><br>
                    Phone: <a href="tel:+************">+44 (0) 20 7692 7003</a>
                </p>
            </div>

            <div class="footer">
                <p>
                    Best regards,<br>
                    Africa Expo Property & Lifestyle Ltd.<br>
                    78 York Street, London, England, W1H 1DP<br>
                    Date: ${currentDate}
                </p>
            </div>
        </div>
    </div>
</body>
</html>
        `
                });
                
                await $app.newMailClient().send(bankDetails);
                console.log(`Bank details email sent to ${record.get('email')}`);
            }

            // If send invoice is updated to true
            if (record.get('send_invoice') && record.get('email_template') === 'invoice') {
                const invoicePercentage = parseFloat(record.get('invoice_percentage')) || 0;
                const invoicePdfBuffer = await generateInvoicePdf({
                    totalAmount, 
                    invoicePercentage, 
                    record
                });
                const invoiceAttachment = new MailAttachment({
                    filename: `invoice-sponsor-${record.id}.pdf`,
                    contentType: 'application/pdf',
                    content: invoicePdfBuffer,
                });
                const invoiceEmail = new MailerMessage({
                    ...baseEmailConfig,
                    subject: 'Invoice for your Sponsorship - Africa Expo 2025',
                    html: `
                    <!DOCTYPE html>
                    <html lang="en">
                    <head>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <title>Invoice</title>
                        <style>
                            * {
                                margin: 0;
                                padding: 0;
                                box-sizing: border-box;
                                font-family: Arial, sans-serif;
                            }
                            body {
                                background-color: #f5f5f5;
                                padding: 20px;
                            }
                            .container {
                                max-width: 600px;
                                margin: 0 auto;
                                background: linear-gradient(to bottom right, #ffffff, #f8f9fa);
                                border-radius: 12px;
                                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                overflow: hidden;
                            }
                            .header {
                                background: linear-gradient(135deg, #4f46e5, #7c3aed);
                                padding: 30px;
                                margin-bottom: 20px;
                            }
                            h2 {
                                color: white;
                                font-size: 24px;
                                margin: 0;
                            }
                            .content {
                                padding: 30px;
                            }
                            p {
                                color: #444;
                                line-height: 1.6;
                                margin-bottom: 15px;
                            }
                            .footer {
                                margin-top: 30px;
                                padding-top: 20px;
                                border-top: 1px solid #eee;
                            }
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <div class="header">
                                <h2>Invoice for Sponsorship</h2>
                            </div>
                            <div class="content">
                                <p>Dear ${record.get('name')},</p>
                                <p>Please find attached your invoice for ${invoicePercentage}% of your Africa Expo 2025 sponsorship package.</p>
                                <p>Payment Details:</p>
                                <ul>
                                    <li>Invoice Amount: £${((totalAmount * invoicePercentage) / 100).toLocaleString()}</li>
                                    <li>Due Date: ${formatDate(new Date())}</li>
                                </ul>
                                <p>If you have any questions about this invoice, please don't hesitate to contact us.</p>
                                <div class="footer">
                                    <p>
                                        Best regards,<br>
                                        Africa Expo Property & Lifestyle Ltd.<br>
                                        78 York Street, London, England, W1H 1DP
                                    </p>
                                </div>
                            </div>
                        </div>
                    </body>
                    </html>
                    `,
                    attachments: [invoiceAttachment],
                });
                await $app.newMailClient().send(invoiceEmail);
                console.log(`Invoice email sent to ${record.get('email')}`);
            }
        } catch (error) {
            console.error('Failed to send email:', error.message);
        }
    };

    // Execute email handling
    handleEmails();
}, "sponsors");

//hooks for exhibitors
onRecordAfterUpdateSuccess((e) => {

    const record = e.record;
    // Helper functions
    const formatDate = (date) => {
        return new Date(date).toLocaleDateString('en-GB', {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
        });
    };
    

    const getPaymentDetails = (totalAmount) => {
        const downPayment = totalAmount * 0.25;
        const remainingAmount = totalAmount * 0.75;
        const orderDate = new Date(record.get('order_date'));
        const dueDate = new Date(orderDate);
        dueDate.setDate(dueDate.getDate() + 30);

        return {
            totalAmount,
            downPayment,
            remainingAmount,
            dueDate: formatDate(dueDate)
        };
    };

    // Handle different update scenarios
    const handleEmails = async () => {
        const currentDate = formatDate(new Date());
        const totalAmount = parseFloat(record.get('package_price'));
        const { downPayment, remainingAmount, dueDate } = getPaymentDetails(totalAmount);

        // Base email configuration
        const baseEmailConfig = {
            from: {
                address: $app.settings().meta.senderAddress,
                name: 'Africa Expo Property & Lifestyle 2025',
            },
            to: [{
                address: record.get('email'),
                name: record.get('name')
            }]
        };

        try {
            // If down payment is updated to true
            if ((record.get('down_payment') && record.get('email_template') === 'down-payment')) {
                const paymentConfirmation = new MailerMessage({
                    ...baseEmailConfig,
                    subject: 'Exhibition Down Payment Received - Africa Expo 2025',
                    html: `
                    <!DOCTYPE html>
                        <html lang="en">
                        <head>
                            <meta charset="UTF-8">
                            <meta name="viewport" content="width=device-width, initial-scale=1.0">
                            <title>Exhibition Payment Confirmation</title>
                            <style>
                                * {
                                    margin: 0;
                                    padding: 0;
                                    box-sizing: border-box;
                                    font-family: Arial, sans-serif;
                                }

                                body {
                                    background-color: #f5f5f5;
                                    padding: 20px;
                                }

                                .container {
                                    max-width: 600px;
                                    margin: 0 auto;
                                    background: linear-gradient(to bottom right, #ffffff, #f8f9fa);
                                    border-radius: 12px;
                                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                    overflow: hidden;
                                }

                                .header {
                                    background: linear-gradient(135deg, #4f46e5, #7c3aed);
                                    padding: 30px;
                                    margin-bottom: 20px;
                                }

                                h2 {
                                    color: white;
                                    font-size: 24px;
                                    margin: 0;
                                }

                                .content {
                                    padding: 0 30px 30px;
                                }

                                h3 {
                                    color: #333;
                                    margin-top: 0;
                                    font-size: 20px;
                                    margin-bottom: 15px;
                                }

                                p {
                                    color: #444;
                                    line-height: 1.6;
                                    margin-bottom: 15px;
                                }

                                .section {
                                    padding: 25px;
                                    margin: 20px 0;
                                    border-radius: 8px;
                                    border: 1px solid rgba(0, 0, 0, 0.1);
                                }

                                .payment-details {
                                    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                                }

                                .payment-terms {
                                    background: linear-gradient(135deg, #fff4e6, #ffe8cc);
                                }

                                .next-steps {
                                    background: linear-gradient(135deg, #e6f3ff, #d0e8ff);
                                }

                                .contact-info {
                                    background: linear-gradient(135deg, #f8f8f8, #f0f0f0);
                                }

                                ul, ol {
                                    margin-left: 20px;
                                    margin-bottom: 15px;
                                }

                                li {
                                    color: #444;
                                    line-height: 1.6;
                                    margin-bottom: 10px;
                                }

                                strong {
                                    color: #333;
                                }

                                .footer {
                                    margin-top: 30px;
                                    padding-top: 20px;
                                    border-top: 1px solid #eee;
                                }

                                .footer p {
                                    font-size: 14px;
                                    color: #666;
                                    line-height: 1.6;
                                }

                                a {
                                    color: #4f46e5;
                                    text-decoration: none;
                                }

                                a:hover {
                                    text-decoration: underline;
                                }

                                @media (max-width: 640px) {
                                    .content {
                                        padding: 0 20px 20px;
                                    }

                                    .header {
                                        padding: 25px 20px;
                                    }

                                    .section {
                                        padding: 20px;
                                    }
                                }
                            </style>
                        </head>
                        <body>
                            <div class="container">
                                <div class="header">
                                    <h2>Exhibition Payment Confirmation</h2>
                                </div>
                                
                                <div class="content">
                                    <p>Dear ${record.get('name')},</p>
                                    
                                    <p>We are delighted to confirm receipt of your down payment for your exhibition at Africa Expo Property & Lifestyle 2025.</p>
                                    
                                    <div class="section payment-details">
                                        <h3>Payment Details</h3>
                                        <p><strong>Package Type:</strong> ${record.get('package_type')}</p>
                                        <p><strong>Total Amount:</strong> £${totalAmount.toLocaleString()}</p>
                                        <p><strong>Down Payment Made:</strong> £${downPayment.toLocaleString()}</p>
                                        <p><strong>Remaining Balance:</strong> £${remainingAmount.toLocaleString()} (75%)</p>
                                        <p><strong>Balance Due Date:</strong> ${dueDate}</p>
                                    </div>

                                    <div class="section payment-terms">
                                        <h3>Important Payment Terms</h3>
                                        <ul>
                                            <li>The remaining balance of £${remainingAmount.toLocaleString()} must be paid on or before ${dueDate}</li>
                                            <li>All fees are inclusive of All prices are inclusive of Value Added Tax(VAT) at the prevailing rate.</li>
                                            <li>${record.get('payment_terms')}</li>
                                        </ul>
                                    </div>

                                    <div class="section next-steps">
                                        <h3>Next Steps</h3>
                                        <ol>
                                            <li>Save this confirmation for your records</li>
                                            <li>Schedule the remaining payment before the due date</li>
                                            <li>Our team will contact you shortly to discuss:
                                                <ul>
                                                    <li>Logo and branding requirements</li>
                                                    <li>Marketing material specifications</li>
                                                    <li>Technical requirements for your exhibition assets</li>
                                                    <li>How to make remaining payments</li>
                                                </ul>
                                            </li>
                                        </ol>
                                    </div>

                                    <div class="section contact-info">
                                        <h3>Your Exhibition Contact</h3>
                                        <p>Your dedicated exhibition manager will be in touch within 2 business days to begin planning your exhibition activation.</p>
                                        
                                        <p>For immediate queries:</p>
                                        <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a><br>
                                        Phone: <a href="tel:+************">+44 (0) 20 7692 7003</a></p>
                                    </div>

                                    <div class="footer">
                                        <p>
                                            Best regards,<br>
                                            Africa Expo Property & Lifestyle Ltd.<br>
                                            78 York Street, London, England, W1H 1DP<br>
                                            Date: ${currentDate}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </body>
                        </html>
                `
                });
                
                await $app.newMailClient().send(paymentConfirmation);
                console.log(`Payment confirmation email sent to ${record.get('email')}`);
            }
            
            // If send invoice is updated to true
            if (record.get('send_invoice') && record.get('email_template') === 'invoice') {
                const invoicePercentage = parseFloat(record.get('invoice_percentage')) || 0;
                const invoicePdfBuffer = await generateInvoicePdf({
                    totalAmount, 
                    invoicePercentage, 
                    record
                });
                const invoiceAttachment = new MailAttachment({
                    filename: `invoice-sponsor-${record.id}.pdf`,
                    contentType: 'application/pdf',
                    content: invoicePdfBuffer,
                });
                const invoiceEmail = new MailerMessage({
                    ...baseEmailConfig,
                    subject: 'Invoice for your Sponsorship - Africa Expo 2025',
                    html: `
                    <!DOCTYPE html>
                    <html lang="en">
                    <head>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <title>Invoice</title>
                        <style>
                            * {
                                margin: 0;
                                padding: 0;
                                box-sizing: border-box;
                                font-family: Arial, sans-serif;
                            }
                            body {
                                background-color: #f5f5f5;
                                padding: 20px;
                            }
                            .container {
                                max-width: 600px;
                                margin: 0 auto;
                                background: linear-gradient(to bottom right, #ffffff, #f8f9fa);
                                border-radius: 12px;
                                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                overflow: hidden;
                            }
                            .header {
                                background: linear-gradient(135deg, #4f46e5, #7c3aed);
                                padding: 30px;
                                margin-bottom: 20px;
                            }
                            h2 {
                                color: white;
                                font-size: 24px;
                                margin: 0;
                            }
                            .content {
                                padding: 30px;
                            }
                            p {
                                color: #444;
                                line-height: 1.6;
                                margin-bottom: 15px;
                            }
                            .footer {
                                margin-top: 30px;
                                padding-top: 20px;
                                border-top: 1px solid #eee;
                            }
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <div class="header">
                                <h2>Invoice for Sponsorship</h2>
                            </div>
                            <div class="content">
                                <p>Dear ${record.get('name')},</p>
                                <p>Please find attached your invoice for ${invoicePercentage}% of your Africa Expo 2025 sponsorship package.</p>
                                <p>Payment Details:</p>
                                <ul>
                                    <li>Invoice Amount: £${((totalAmount * invoicePercentage) / 100).toLocaleString()}</li>
                                    <li>Due Date: ${formatDate(new Date())}</li>
                                </ul>
                                <p>If you have any questions about this invoice, please don't hesitate to contact us.</p>
                                <div class="footer">
                                    <p>
                                        Best regards,<br>
                                        Africa Expo Property & Lifestyle Ltd.<br>
                                        78 York Street, London, England, W1H 1DP
                                    </p>
                                </div>
                            </div>
                        </div>
                    </body>
                    </html>
                    `,
                    attachments: [invoiceAttachment],
                });
                await $app.newMailClient().send(invoiceEmail);
                console.log(`Invoice email sent to ${record.get('email')}`);
            }
            
            // If send_bank_details is updated to true
            if ((record.get('send_bank_details') && record.get('email_template') === 'bank-details')) {
                const bankDetails = new MailerMessage({
                    ...baseEmailConfig,
                    subject: 'Bank Transfer Details - Africa Expo 2025 Exhibition',
                    html: `
                    <!DOCTYPE html>
                    <html lang="en">
                    <head>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <title>Bank Transfer Instructions</title>
                        <style>
                            * {
                                margin: 0;
                                padding: 0;
                                box-sizing: border-box;
                                font-family: Arial, sans-serif;
                            }

                            body {
                                background: #f5f5f5;
                                padding: 20px;
                            }

                            .container {
                                max-width: 800px;
                                margin: 0 auto;
                                background: linear-gradient(135deg, #ffffff, #f8f9fa);
                                border-radius: 12px;
                                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                overflow: hidden;
                            }

                            .header {
                                background: linear-gradient(135deg, #4f46e5, #7c3aed);
                                color: white;
                                padding: 40px;
                                text-align: center;
                            }

                            .header h1 {
                                font-size: 28px;
                                margin-bottom: 10px;
                            }

                            .header h2 {
                                font-size: 20px;
                                font-weight: normal;
                            }

                            .content {
                                padding: 30px;
                            }

                            .section {
                                margin-bottom: 30px;
                                padding: 25px;
                                border-radius: 8px;
                            }

                            .section h3 {
                                color: #1f2937;
                                margin-bottom: 20px;
                                font-size: 20px;
                            }

                            .sponsorship-package {
                                background: linear-gradient(135deg, #eff6ff, #eef2ff);
                                border: 1px solid #dbeafe;
                            }

                            .bank-details {
                                background: linear-gradient(135deg, #f5f3ff, #faf5ff);
                                border: 1px solid #e9d5ff;
                            }

                            .bank-details-inner {
                                background: white;
                                padding: 20px;
                                border-radius: 6px;
                            }

                            .payment-terms {
                                background: linear-gradient(135deg, #ecfdf5, #f0fdf4);
                                border: 1px solid #d1fae5;
                            }

                            .next-steps {
                                background: linear-gradient(135deg, #fffbeb, #fef3c7);
                                border: 1px solid #fde68a;
                            }

                            .help-section {
                                background: linear-gradient(135deg, #f8fafc, #f1f5f9);
                                border: 1px solid #e2e8f0;
                            }

                            .footer {
                                margin-top: 30px;
                                padding-top: 20px;
                                border-top: 1px solid #e5e7eb;
                                color: #4b5563;
                                font-size: 14px;
                                line-height: 1.6;
                            }

                            p {
                                margin-bottom: 15px;
                                line-height: 1.6;
                                color: #374151;
                            }

                            ul, ol {
                                margin-left: 20px;
                                margin-bottom: 15px;
                            }

                            li {
                                margin-bottom: 10px;
                                color: #374151;
                            }

                            .bank-details-grid {
                                display: grid;
                                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                                gap: 15px;
                            }

                            .detail-item {
                                margin-bottom: 10px;
                            }

                            .detail-item strong {
                                color: #1f2937;
                            }

                            a {
                                color: #4f46e5;
                                text-decoration: none;
                            }

                            a:hover {
                                text-decoration: underline;
                            }

                            @media (max-width: 640px) {
                                .container {
                                    margin: 10px;
                                }

                                .header {
                                    padding: 30px 20px;
                                }

                                .content {
                                    padding: 20px;
                                }

                                .section {
                                    padding: 20px;
                                }
                            }
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <div class="header">
                                <h1>Bank Transfer Instructions</h1>
                                <h2>Africa Expo Property & Lifestyle 2025 Exhibition</h2>
                            </div>

                            <div class="content">
                                <p>Dear ${record.get('name')},</p>
                                
                                <p>Thank you for choosing to exhibit Africa Expo Property & Lifestyle 2025. 
                                Below are your exhibition details and bank transfer information.</p>

                                <div class="section sponsorship-package">
                                    <p><strong>Package Type:</strong> ${record.get('package_type')}</p>
                                    <p><strong>Total Amount:</strong> £${totalAmount.toLocaleString()}</p>
                                    <p><strong>Down Payment Made:</strong> £${downPayment.toLocaleString()}</p>
                                    <p><strong>Remaining Balance:</strong> £${remainingAmount.toLocaleString()} (75%)</p>
                                    <p><strong>Balance Due Date:</strong> ${dueDate}</p>
                                </div>

                                <div class="section bank-details">
                                    <h3>Bank Transfer Details</h3>
                                    <div class="bank-details-inner">
                                        <div class="bank-details-grid">
                                            
                                            <div class="detail-item">
                                                <strong>Account Name:</strong> Ghana Property & Lifestyle Expo Ltd
                                            </div>
                                            <div class="detail-item">
                                                <strong>Sort Code:</strong> 20-90-74
                                            </div>
                                            <div class="detail-item">
                                                <strong>Account Number:</strong> ********
                                            </div>
                                            <div class="detail-item">
                                                <strong>IBAN:</strong> GB16 BUKB 2090 7443 8866 03
                                            </div>
                                            <div class="detail-item">
                                                <strong>SWIFT/BIC:</strong> BUKBGB22
                                            </div>
                                        </div>
                                        <div style="margin-top: 20px;">
                                            <div class="detail-item">
                                                <strong>Address:</strong> 78 York Street, London, England, W1H 1DP
                                            </div>
                                            <div class="detail-item">
                                                <strong>Bank Address:</strong> Clapham Junction Branch. 7 St John's Hill Clapham Junction SW11 1TR
                                            </div>
                                            <div class="detail-item">
                                                <strong>Reference:</strong> EXHIBITOR-${record.id}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="section payment-terms">
                                    <h3>Payment Terms</h3>
                                    <ul>
                                        <li>Please include reference EXHIBITOR-${record.id} in your transfer</li>
                                        <li>Initial payment (25%): £${downPayment.toLocaleString()}</li>
                                        <li>Payment deadline: ${dueDate}</li>
                                        <li>Balance payment (75%): Due within 30 days</li>
                                        <li>All fees are inclusive of All prices are inclusive of Value Added Tax(VAT) at the prevailing rate.</li>
                                    </ul>
                                </div>

                                <div class="section next-steps">
                                    <h3>Next Steps</h3>
                                    <ol>
                                        <li>Complete the bank transfer using the details above</li>
                                        <li>Email the transfer <NAME_EMAIL></li>
                                        <li>We will send a payment receipt once the transfer is confirmed</li>
                                        <li>Our team will then contact you regarding:
                                            <ul>
                                                <li>Logo and branding requirements</li>
                                                <li>Marketing material specifications</li>
                                                <li>Technical specifications for your sponsorship assets</li>
                                                <li>How to make remaining payments</li>
                                            </ul>
                                        </li>
                                    </ol>
                                </div>

                                <div class="section help-section">
                                    <h3>Need Help?</h3>
                                    <p>For any questions about the payment process, please contact us:</p>
                                    <p>
                                        Email: <a href="mailto:<EMAIL>"><EMAIL></a><br>
                                        Phone: <a href="tel:+************">+44 (0) 20 7692 7003</a>
                                    </p>
                                </div>

                                <div class="footer">
                                    <p>
                                        Best regards,<br>
                                        Africa Expo Property & Lifestyle Ltd.<br>
                                        78 York Street, London, England, W1H 1DP<br>
                                        Date: ${currentDate}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </body>
                    </html>
                    `
                });
                
                await $app.newMailClient().send(bankDetails);
                console.log(`Bank details email sent to ${record.get('email')}`);
            }
        } catch (error) {
            console.error('Failed to send email:', error.message);
        }
    };

    // Execute email handling
    handleEmails();
}, "exhibitors");


//hooks for press_applications
onRecordAfterCreateSuccess((e) => {
    const record = e.record;
    const currentDate = new Date().toLocaleDateString('en-GB', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
    });

    const message = new MailerMessage({
        from: {
            address: $app.settings().meta.senderAddress,
            name: $app.settings().meta.senderName,
        },
        to: [{
            address: record.get('email'),
            name: `${record.get('firstName')} ${record.get('lastName')}`
        }],
        subject: `Press Application Received - Africa Expo Property & Lifestyle 2025`,
        html: `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Press Application Confirmation</title>
                <style>
                    * {
                        margin: 0;
                        padding: 0;
                        box-sizing: border-box;
                        font-family: Arial, sans-serif;
                    }

                    body {
                        background-color: #f5f5f5;
                        padding: 20px;
                    }

                    .container {
                        max-width: 600px;
                        margin: 0 auto;
                        background: linear-gradient(to bottom right, #ffffff, #f8f9fa);
                        border-radius: 12px;
                        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                        overflow: hidden;
                    }

                    .header {
                        background: linear-gradient(135deg, #4f46e5, #7c3aed);
                        padding: 30px;
                        margin-bottom: 20px;
                    }

                    h2 {
                        color: white;
                        font-size: 24px;
                        margin: 0;
                    }

                    .content {
                        padding: 0 30px 30px;
                    }

                    p {
                        color: #444;
                        line-height: 1.6;
                        margin-bottom: 15px;
                    }

                    .details-box {
                        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                        padding: 25px;
                        margin: 20px 0;
                        border-radius: 8px;
                        border: 1px solid rgba(0, 0, 0, 0.1);
                    }

                    .details-box p {
                        margin-bottom: 10px;
                    }

                    .details-box p:last-child {
                        margin-bottom: 0;
                    }

                    ul {
                        margin-left: 20px;
                        margin-bottom: 15px;
                        color: #444;
                    }

                    li {
                        line-height: 1.6;
                        margin-bottom: 10px;
                    }

                    strong {
                        color: #333;
                    }

                    .footer {
                        margin-top: 30px;
                        padding-top: 20px;
                        border-top: 1px solid #eee;
                    }

                    .footer p {
                        font-size: 14px;
                        color: #666;
                        line-height: 1.6;
                    }

                    a {
                        color: #4f46e5;
                        text-decoration: none;
                    }

                    a:hover {
                        text-decoration: underline;
                    }

                    @media (max-width: 640px) {
                        .content {
                            padding: 0 20px 20px;
                        }

                        .header {
                            padding: 25px 20px;
                        }

                        .details-box {
                            padding: 20px;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h2>Press Application Confirmation</h2>
                    </div>
                    
                    <div class="content">
                        <p>Dear ${record.get('firstName')} ${record.get('lastName')},</p>
                        
                        <p>Thank you for your interest in covering the Africa Expo Property & Lifestyle 2025. We have received your press application with the following details:</p>
                        
                        <div class="details-box">
                            <p><strong>Reference Number:</strong> ${record.get('referenceNumber')}</p>
                            <p><strong>Organization:</strong> ${record.get('organization')}</p>
                            <p><strong>Job Title:</strong> ${record.get('jobTitle')}</p>
                            <p><strong>Media Type:</strong> ${record.get('mediaType')}</p>
                        </div>

                        <p>Your application is currently under review by our press team. We will assess your credentials and intended coverage and get back to you within 5 working days.</p>

                        <p>Please note:</p>
                        <ul>
                            <li>Keep your reference number safe for future correspondence</li>
                            <li>Ensure your press card (${record.get('pressCardIssuer')}) remains valid for the event</li>
                            <li>We may contact you for additional information if needed</li>
                        </ul>

                        <p>If you have any questions in the meantime, please don't hesitate to contact our press office at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

                        <div class="footer">
                            <p>
                                Best regards,<br>
                                Press Relations Team<br>
                                Africa Expo Property & Lifestyle Ltd.<br>
                                Date: ${currentDate}
                            </p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
        `
    });

    try {
        $app.newMailClient().send(message);
        console.log(`Confirmation email sent to ${record.get('email')}`);
    } catch (error) {
        console.error(`Failed to send confirmation email: ${error.message}`);
    }
}, "press_applications");



// Handle application approval
onRecordAfterUpdateSuccess((e) => {
    const record = e.record;
    const oldRecord = e.oldRecord;
    
    // Check if record was just approved 
    if (record.get('approved') === true ) {
        const currentDate = new Date().toLocaleDateString('en-GB', {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
        });
    
        const message = new MailerMessage({
            from: {
                address: $app.settings().meta.senderAddress,
                name: $app.settings().meta.senderName,
            },
            to: [{
                address: record.get('email'),
                name: `${record.get('firstName')} ${record.get('lastName')}`
            }],
            subject: `Press Application Approved - Africa Expo Property & Lifestyle 2025`,
            html: `
                <!DOCTYPE html>
                <html lang="en">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Press Application Approved</title>
                    <style>
                        * {
                            margin: 0;
                            padding: 0;
                            box-sizing: border-box;
                            font-family: Arial, sans-serif;
                        }
    
                        body {
                            background-color: #f5f5f5;
                            padding: 20px;
                        }
    
                        .container {
                            max-width: 600px;
                            margin: 0 auto;
                            background: linear-gradient(to bottom right, #ffffff, #f8f9fa);
                            border-radius: 12px;
                            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                            overflow: hidden;
                        }
    
                        .header {
                            background: linear-gradient(135deg, #4f46e5, #7c3aed);
                            padding: 30px;
                            margin-bottom: 20px;
                        }
    
                        h2 {
                            color: white;
                            font-size: 24px;
                            margin: 0;
                        }
    
                        .content {
                            padding: 0 30px 30px;
                        }
    
                        p {
                            color: #444;
                            line-height: 1.6;
                            margin-bottom: 15px;
                        }
    
                        .details-box {
                            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                            padding: 25px;
                            margin: 20px 0;
                            border-radius: 8px;
                            border: 1px solid rgba(0, 0, 0, 0.1);
                        }
    
                        .details-box p {
                            margin-bottom: 10px;
                        }
    
                        .details-box p:last-child {
                            margin-bottom: 0;
                        }
    
                        ul {
                            margin-left: 20px;
                            margin-bottom: 15px;
                            color: #444;
                        }
    
                        li {
                            line-height: 1.6;
                            margin-bottom: 10px;
                        }
    
                        strong {
                            color: #333;
                        }
    
                        .footer {
                            margin-top: 30px;
                            padding-top: 20px;
                            border-top: 1px solid #eee;
                        }
    
                        .footer p {
                            font-size: 14px;
                            color: #666;
                            line-height: 1.6;
                        }
    
                        a {
                            color: #4f46e5;
                            text-decoration: none;
                        }
    
                        a:hover {
                            text-decoration: underline;
                        }
    
                        @media (max-width: 640px) {
                            .content {
                                padding: 0 20px 20px;
                            }
    
                            .header {
                                padding: 25px 20px;
                            }
    
                            .details-box {
                                padding: 20px;
                            }
                        }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <h2>Press Application Approved</h2>
                        </div>
                        
                        <div class="content">
                            <p>Dear ${record.get('firstName')} ${record.get('lastName')},</p>
                            
                            <p>We are pleased to inform you that your press application for Africa Expo Property & Lifestyle 2025 has been approved.</p>
                            
                            <div class="details-box">
                                <p><strong>Reference Number:</strong> ${record.get('referenceNumber')}</p>
                                <p><strong>Organization:</strong> ${record.get('organization')}</p>
                                <p><strong>Media Type:</strong> ${record.get('mediaType')}</p>
                                <p><strong>Press Pass Status:</strong> Approved</p>
                            </div>
    
                            <p>Next Steps:</p>
                            <ul>
                                <li>You will receive a separate email with your press pass and event credentials within 48 hours</li>
                                <li>Review our press guidelines and media kit which will be attached to the credentials email</li>
                                <li>Mark your calendar for our pre-event press briefing (details to follow)</li>
                            </ul>
    
                            <p>Important Information:</p>
                            <ul>
                                <li>Your press pass must be displayed at all times during the event</li>
                                <li>Bring a valid government-issued ID and your press card for verification</li>
                                <li>Access to press areas will be strictly controlled</li>
                            </ul>
    
                            <p>If you have any questions or special requirements, please contact our press office at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
    
                            <div class="footer">
                                <p>
                                    Best regards,<br>
                                    Press Relations Team<br>
                                    Africa Expo Property & Lifestyle Ltd.<br>
                                    Date: ${currentDate}
                                </p>
                            </div>
                        </div>
                    </div>
                </body>
                </html>
            `
        });
    
        try {
            $app.newMailClient().send(message);
            console.log(`Approval email sent to ${record.get('email')}`);
        } catch (error) {
            console.error(`Failed to send approval email: ${error.message}`);
        }
    }
}, "press_applications");

function handleExhibitorOrderCreation(e) {
    const record = e.record;
    const currentDate = new Date().toLocaleDateString('en-GB', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
    });

    // Get user details
    const user = $app.findFirstRecordByFilter('users', `id = '${record.get('user')}'`);
    const address = JSON.parse(record.get('address'));
    const amenities = JSON.parse(record.get('amenities'));

    // Send notification to staff
    const staffNotification = new MailerMessage({
        from: {
            address: $app.settings().meta.senderAddress,
            name: 'Africa Expo Property & Lifestyle 2025'
        },
        to: [
            { address: '<EMAIL>' },
            { address: '<EMAIL>' },
            { address: '<EMAIL>' }
        ],
        subject: `New Exhibition Order - ${record.get('name')}`,
        html: `
            <h2>New Exhibition Order Details</h2>
            
            <h3>User Details</h3>
            <p>Name: ${user.get('fullName')}</p>
            <p>Email: ${user.get('email')}</p>
            <p>Created: ${new Date(user.get('created')).toLocaleDateString()}</p>
            
            <h3>Business Details</h3>
            <p>Exhibitor Name: ${record.get('name')}</p>
            <p>Address: ${address.addressLine1}, 
               ${address.addressLine2 || ''} 
               ${address.city}, 
               ${address.postalCode}, 
               ${address.country}</p>
            <p>Phone: ${record.get('phone')}</p>
            <p>Email: ${record.get('email')}</p>
            
            <h3>Order Details</h3>
            <p>Package Type: ${record.get('package_type')}</p>
            <p>Total Amount: £${record.get('package_price').toLocaleString()}</p>
            <p>Down Payment: £${(record.get('package_price') * 0.25).toLocaleString()}</p>
            <p>Order Date: ${new Date(record.get('order_date')).toLocaleDateString()}</p>
            <p>Order By: ${record.get('order_by') || 'N/A'}</p>
            <p>Order Taken By: ${record.get('order_taken_by') || 'N/A'}</p>
            
            <h3>Terms and Conditions</h3>
            <p>Payment Terms: ${record.get('payment_terms') || 'Standard payment terms apply'}</p>
            <p>Discount Information: ${record.get('discount_info') || 'No discount applied'}</p>
        `,
        attachments: [{
            filename: `order-${record.get('name')}.pdf`,
            content: Buffer.from(JSON.stringify({
                userDetails: {
                    name: user.get('name'),
                    email: user.get('email'),
                    created: user.get('created')
                },
                businessDetails: {
                    exhibitorName: record.get('name'),
                    address: {
                        addressLine1: address.addressLine1,
                        addressLine2: address.addressLine2 || '',
                        city: address.city,
                        postalCode: address.postalCode,
                        country: address.country
                    },
                    contactInformation: {
                        phone: record.get('phone'),
                        email: record.get('email')
                    },
                    orderInformation: {
                        packageType: record.get('package_type'),
                        packagePrice: `£${record.get('package_price').toLocaleString()}`,
                        downPayment: `£${(record.get('package_price') * 0.25).toLocaleString()}`,
                        remainingBalance: `£${(record.get('package_price') * 0.75).toLocaleString()}`,
                        orderDate: new Date(record.get('order_date')).toLocaleDateString(),
                        orderBy: record.get('order_by') || 'N/A',
                        orderTakenBy: record.get('order_taken_by') || 'N/A'
                    },
                    termsAndConditions: {
                        paymentTerms: record.get('payment_terms'),
                        termsAccepted: record.get('terms_accepted')
                    },
                    packageAmenities: amenities
                }
            }, null, 2))
        }]
    });

    try {
        // Send staff notification
        $app.newMailClient().send(staffNotification);
        console.log(`Order confirmation email sent for exhibitor ${record.get('name')}`);
    } catch (error) {
        console.error('Failed to send order confirmation email:', error.message);
    }
}

// Register the handler for exhibitor creation
onRecordAfterCreateSuccess(handleExhibitorOrderCreation, "exhibitors");

function handleSponsorOrderCreation(e) {
    const record = e.record;
    const currentDate = new Date().toLocaleDateString('en-GB', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
    });

    // Get user details
    const user = $app.findFirstRecordByFilter('users', `id = '${record.get('user')}'`);
    const sponsorshipOptions = JSON.parse(record.get('sponsorship_menu_options'));

    // Send notification to staff
    const staffNotification = new MailerMessage({
        from: {
            address: $app.settings().meta.senderAddress,
            name: 'Africa Expo Property & Lifestyle 2025'
        },
        to: [
            { address: '<EMAIL>' },
            { address: '<EMAIL>' },
            { address: '<EMAIL>' }
        ],
        subject: `New Sponsorship Order - ${record.get('name')}`,
        html: `
            <h2>New Sponsorship Order Details</h2>
            
            <h3>User Details</h3>
            <p>Name: ${user.get('fullName')}</p>
            <p>Email: ${user.get('email')}</p>
            <p>Created: ${new Date(user.get('created')).toLocaleDateString()}</p>
            
            <h3>Business Details</h3>
            <p>Sponsor Name: ${record.get('name')}</p>
            <p>Phone: ${record.get('phone')}</p>
            <p>Email: ${record.get('email')}</p>
            
            <h3>Order Details</h3>
            <p>Sponsorship Options:</p>
            <ul>
                ${sponsorshipOptions.map(option => `<li>${option}</li>`).join('')}
            </ul>
            <p>Total Amount: £${record.get('total_fee').toLocaleString()}</p>
            <p>Down Payment: £${(record.get('total_fee') * 0.25).toLocaleString()}</p>
            <p>Order Date: ${new Date(record.get('order_date')).toLocaleDateString()}</p>
            <p>Order By: ${record.get('ordered_by') || 'N/A'}</p>
            <p>Order Taken By: ${record.get('taken_by') || 'N/A'}</p>
            <p>Season: ${record.get('season') || 'Current Season'}</p>
            
            <h3>Terms and Conditions</h3>
            <p>Payment Terms: ${record.get('payment_terms') || 'Standard payment terms apply'}</p>
        `,
        attachments: [{
            filename: `sponsor-order-${record.get('name')}.pdf`,
            content: Buffer.from(JSON.stringify({
                userDetails: {
                    name: user.get('name'),
                    email: user.get('email'),
                    created: user.get('created')
                },
                businessDetails: {
                    sponsorName: record.get('name'),
                    contactInformation: {
                        phone: record.get('phone'),
                        email: record.get('email')
                    },
                    orderInformation: {
                        sponsorshipOptions: sponsorshipOptions,
                        totalFee: `£${record.get('total_fee').toLocaleString()}`,
                        downPayment: `£${(record.get('total_fee') * 0.25).toLocaleString()}`,
                        remainingBalance: `£${(record.get('total_fee') * 0.75).toLocaleString()}`,
                        orderDate: new Date(record.get('order_date')).toLocaleDateString(),
                        orderBy: record.get('ordered_by') || 'N/A',
                        orderTakenBy: record.get('taken_by') || 'N/A',
                        season: record.get('season') || 'Current Season'
                    },
                    termsAndConditions: {
                        paymentTerms: record.get('payment_terms'),
                        termsAccepted: record.get('terms_accepted')
                    }
                }
            }, null, 2))
        }]
    });

    try {
        // Send staff notification
        $app.newMailClient().send(staffNotification);
        console.log(`Order confirmation email sent for sponsor ${record.get('name')}`);
    } catch (error) {
        console.error('Failed to send order confirmation email:', error.message);
    }
}

// Register the handler for sponsor creation
onRecordAfterCreateSuccess(handleSponsorOrderCreation, "sponsors");


