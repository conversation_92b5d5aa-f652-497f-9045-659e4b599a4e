---
import MainLayout from '../layouts/MainLayout.astro';
import Services from '../components/sections/Services.astro';
import Features from '../components/sections/Features.astro';
import { ServerContentFetcher } from '../lib/server-content.ts';

// Fetch services page content from database with fallbacks
const servicesData = await ServerContentFetcher.getServicesPageContent('en');
const servicesFeaturesData = await ServerContentFetcher.getServicesFeaturesContent('en');
---

<MainLayout 
  title="Services - PTBL | Power Telco Business Limited"
  description="Comprehensive fiber network services including Leased Line Services, Network Planning, Network Optimization, and infrastructure solutions across ECG's operational area."
  lang="en"
  showPageHeader={true}
  pageTitle="Our Services"
  pageSubtitle="Comprehensive network solutions powered by ECG's fiber infrastructure"
>
  <Services {...servicesData} />
  <Features {...servicesFeaturesData} />
</MainLayout>
