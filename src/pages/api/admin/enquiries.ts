import type { APIRoute } from 'astro';
import { ServerPocketBase } from '../../../lib/server-content.ts';
import { withAdminApiMiddleware } from '../../../lib/api-middleware.ts';

const enquiriesHandler: APIRoute = async ({ request }) => {
  const pb = new ServerPocketBase();
  const url = new URL(request.url);

  try {
    switch (request.method) {
      case 'GET':
        return await handleGetEnquiries(pb, url);
      case 'PATCH':
        return await handleUpdateEnquiry(pb, request);
      case 'DELETE':
        return await handleDeleteEnquiry(pb, url);
      default:
        return new Response(JSON.stringify({
          error: 'Method not allowed'
        }), {
          status: 405,
          headers: { 'Content-Type': 'application/json' }
        });
    }
  } catch (error) {
    console.error('Admin enquiries API error:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

async function handleGetEnquiries(pb: ServerPocketBase, url: URL) {
  const status = url.searchParams.get('status');
  const page = parseInt(url.searchParams.get('page') || '1');
  const perPage = parseInt(url.searchParams.get('perPage') || '50');

  try {
    let filter = '';
    if (status) {
      filter = `status="${status}"`;
    }

    const response = await fetch(`${pb['baseUrl']}/api/collections/enquiries/records?sort=-created&page=${page}&perPage=${perPage}${filter ? `&filter=${encodeURIComponent(filter)}` : ''}`);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    return new Response(JSON.stringify(data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error fetching enquiries:', error);
    throw error;
  }
}

async function handleUpdateEnquiry(pb: ServerPocketBase, request: Request) {
  const url = new URL(request.url);
  const id = url.searchParams.get('id');
  
  if (!id) {
    return new Response(JSON.stringify({
      error: 'Enquiry ID is required'
    }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  try {
    const updateData = await request.json();
    
    // Validate update data
    const allowedFields = ['status', 'priority', 'notes'];
    const filteredData: Record<string, unknown> = {};
    
    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        filteredData[field] = updateData[field];
      }
    }

    if (Object.keys(filteredData).length === 0) {
      return new Response(JSON.stringify({
        error: 'No valid fields to update'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const response = await fetch(`${pb['baseUrl']}/api/collections/enquiries/records/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(filteredData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const updatedRecord = await response.json();
    
    return new Response(JSON.stringify({
      success: true,
      data: updatedRecord
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error updating enquiry:', error);
    throw error;
  }
}

async function handleDeleteEnquiry(pb: ServerPocketBase, url: URL) {
  const id = url.searchParams.get('id');
  
  if (!id) {
    return new Response(JSON.stringify({
      error: 'Enquiry ID is required'
    }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  try {
    const response = await fetch(`${pb['baseUrl']}/api/collections/enquiries/records/${id}`, {
      method: 'DELETE'
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return new Response(JSON.stringify({
      success: true,
      message: 'Enquiry deleted successfully'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error deleting enquiry:', error);
    throw error;
  }
}

// Export with admin middleware (includes authentication and rate limiting exemption)
export const GET = withAdminApiMiddleware(enquiriesHandler);
export const PATCH = withAdminApiMiddleware(enquiriesHandler);
export const DELETE = withAdminApiMiddleware(enquiriesHandler);
