import type { APIRoute } from 'astro';
import { withPublicApiMiddleware, getClientIP } from '../../../lib/api-middleware.ts';

const rateLimitTestHandler: APIRoute = async ({ request }) => {
  try {
    const clientIP = getClientIP(request);
    const timestamp = new Date().toISOString();
    const method = request.method;

    // Get rate limit headers from the response (these will be added by middleware)
    const testData = {
      message: 'Rate limit test endpoint',
      timestamp,
      method,
      clientIP,
      note: 'This endpoint is subject to rate limiting (100 requests per 15 minutes)',
      instructions: {
        testing: 'Make multiple rapid requests to test rate limiting',
        expected: 'After 100 requests in 15 minutes, you should receive HTTP 429',
        headers: 'Check X-RateLimit-Remaining and X-RateLimit-Reset headers'
      }
    };

    return new Response(JSON.stringify(testData), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });
  } catch (error) {
    console.error('Rate limit test API error:', error);
    
    return new Response(JSON.stringify({
      error: 'Internal server error',
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

// Export with public API middleware (includes rate limiting)
export const GET = withPublicApiMiddleware(rateLimitTestHandler);
export const POST = withPublicApiMiddleware(rateLimitTestHandler);
