import type { APIRoute } from 'astro';
import { withPublicApiMiddleware } from '../../../lib/api-middleware.ts';

const statusHandler: APIRoute = async ({ request }) => {
  try {
    const timestamp = new Date().toISOString();
    const method = request.method;

    // Simple status endpoint that returns system information
    const statusData = {
      status: 'operational',
      timestamp,
      method,
      message: 'PTBL API is running normally',
      version: '1.0.0',
      services: {
        database: 'operational',
        api: 'operational',
        rateLimit: 'active'
      }
    };

    return new Response(JSON.stringify(statusData), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });
  } catch (error) {
    console.error('Status API error:', error);
    
    return new Response(JSON.stringify({
      status: 'error',
      timestamp: new Date().toISOString(),
      message: 'Service temporarily unavailable'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

// Export with public API middleware (includes rate limiting for non-admin users)
export const GET = withPublicApiMiddleware(statusHandler);
export const POST = withPublicApiMiddleware(statusHandler);
