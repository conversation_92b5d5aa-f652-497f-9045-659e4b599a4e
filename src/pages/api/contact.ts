import type { APIRoute } from 'astro';
import { ServerPocketBase } from '../../lib/server-content.ts';

export const POST: APIRoute = async ({ request }) => {
  try {
    const data = await request.json();
    
    // Validate required fields
    if (!data.name || !data.email || !data.subject || !data.message) {
      return new Response(JSON.stringify({ 
        error: 'Missing required fields' 
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // Create PocketBase instance
    const pb = new ServerPocketBase();
    
    // Prepare enquiry data
    const enquiryData = {
      name: data.name.trim(),
      email: data.email.trim().toLowerCase(),
      phone: data.phone ? data.phone.trim() : '',
      subject: data.subject.trim(),
      message: data.message.trim(),
      status: 'new',
      priority: data.priority || 'medium'
    };

    // Create the enquiry record
    const record = await pb.createRecord('enquiries', enquiryData);
    
    return new Response(JSON.stringify({ 
      success: true, 
      id: record.id 
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });

  } catch (error) {
    console.error('Contact form submission error:', error);
    
    return new Response(JSON.stringify({ 
      error: 'Failed to submit enquiry' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
