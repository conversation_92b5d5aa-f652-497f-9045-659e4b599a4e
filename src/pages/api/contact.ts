import type { APIRoute } from 'astro';
import { ServerPocketBase } from '../../lib/server-content.ts';
import { contactFormLimiter, createRateLimitResponse, addRateLimitHeaders } from '../../lib/rate-limiter.ts';
import { withLogging } from '../../lib/api-middleware.ts';

const contactHandler: APIRoute = async ({ request }) => {
  try {
    // Check rate limit first
    const rateLimitResult = contactFormLimiter.check(request);

    if (!rateLimitResult.allowed) {
      return createRateLimitResponse(
        rateLimitResult.resetTime!,
        'Too many contact form submissions. Please wait before submitting again. Limit: 3 submissions per hour.'
      );
    }

    const data = await request.json();
    
    // Validate required fields
    if (!data.name || !data.email || !data.subject || !data.message) {
      return new Response(JSON.stringify({ 
        error: 'Missing required fields' 
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // Create PocketBase instance
    const pb = new ServerPocketBase();
    
    // Prepare enquiry data
    const enquiryData = {
      name: data.name.trim(),
      email: data.email.trim().toLowerCase(),
      phone: data.phone ? data.phone.trim() : '',
      subject: data.subject.trim(),
      message: data.message.trim(),
      status: 'new',
      priority: data.priority || 'medium'
    };

    // Create the enquiry record
    const record = await pb.createRecord('enquiries', enquiryData);

    // Create successful response
    const response = new Response(JSON.stringify({
      success: true,
      id: record.id
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Add rate limit headers to successful response
    return addRateLimitHeaders(response, rateLimitResult.remaining!, rateLimitResult.resetTime!);

  } catch (error) {
    console.error('Contact form submission error:', error);
    
    return new Response(JSON.stringify({ 
      error: 'Failed to submit enquiry' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Export the handler with logging middleware
export const POST = withLogging(contactHandler, 'info');
