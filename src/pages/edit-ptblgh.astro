---
import BaseLayout from '../layouts/BaseLayout.astro';

// This will be handled client-side for authentication
---

<BaseLayout 
  title="PTBL Content Management - Admin Dashboard"
  description="Admin dashboard for managing PTBL website content"
>
  <!-- Loading screen -->
  <div id="loadingScreen" class="fixed inset-0 bg-white z-50 flex items-center justify-center">
    <div class="text-center">
      <div class="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mb-4"></div>
      <p class="text-gray-600">Loading admin dashboard...</p>
    </div>
  </div>

  <!-- Main admin interface -->
  <div id="adminInterface" class="hidden min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-4">
          <div class="flex items-center">
            <h1 class="text-2xl font-bold text-gray-900">PTBL Content Management</h1>
          </div>
          <div class="flex items-center space-x-4">
            <span id="userInfo" class="text-sm text-gray-600"></span>
            <button 
              id="logoutButton"
              class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
    </header>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Navigation Tabs -->
      <div class="border-b border-gray-200 mb-8">
        <nav class="-mb-px flex space-x-8">
          <button 
            class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
            data-tab="content"
          >
            Content Sections
          </button>
          <button 
            class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
            data-tab="images"
          >
            Images
          </button>
          <button 
            class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
            data-tab="settings"
          >
            Settings
          </button>
        </nav>
      </div>

      <!-- Content Sections Tab -->
      <div id="contentTab" class="tab-content hidden">
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
              <h2 class="text-lg font-medium text-gray-900">Content Sections</h2>
              <div class="flex space-x-2">
                <select id="languageSelect" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                  <option value="en">English</option>
                  <option value="fr">French</option>
                </select>
                <button 
                  id="addContentButton"
                  class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Add Section
                </button>
              </div>
            </div>
          </div>
          <div id="contentList" class="divide-y divide-gray-200">
            <!-- Content sections will be loaded here -->
          </div>
        </div>
      </div>

      <!-- Images Tab -->
      <div id="imagesTab" class="tab-content hidden">
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
              <h2 class="text-lg font-medium text-gray-900">Site Images</h2>
              <button 
                id="uploadImageButton"
                class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Upload Image
              </button>
            </div>
          </div>
          <div id="imageGrid" class="p-6 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            <!-- Images will be loaded here -->
          </div>
        </div>
      </div>

      <!-- Settings Tab -->
      <div id="settingsTab" class="tab-content hidden">
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Settings</h2>
          </div>
          <div class="p-6">
            <div class="space-y-6">
              <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">PocketBase Configuration</h3>
                <div class="bg-gray-50 p-4 rounded-md">
                  <p class="text-sm text-gray-600 mb-2">PocketBase URL:</p>
                  <code class="text-sm bg-white px-2 py-1 rounded border" id="pocketbaseUrl"></code>
                </div>
              </div>
              
              <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">User Information</h3>
                <div id="userDetails" class="bg-gray-50 p-4 rounded-md">
                  <!-- User details will be loaded here -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modals will be inserted here by JavaScript -->
  <div id="modalContainer"></div>

  <script>
    import { AuthManager, ContentManager } from '../lib/pocketbase.ts';
    import pb from '../lib/pocketbase.ts';
    import { Modal, FormBuilder, Notification } from '../components/admin/Modal.ts';

    // Check authentication on page load
    document.addEventListener('DOMContentLoaded', async () => {
      const loadingScreen = document.getElementById('loadingScreen');
      const adminInterface = document.getElementById('adminInterface');

      console.log('Admin dashboard loading...');
      console.log('localStorage auth data:', localStorage.getItem('pocketbase_auth'));

      try {
        // Check if user is authenticated
        const isAuthenticated = AuthManager.isAuthenticated();
        console.log('Authentication status:', isAuthenticated);
        console.log('Auth store token:', pb.authStore.token ? 'Present' : 'Missing');
        console.log('Auth store record:', pb.authStore.record ? 'Present' : 'Missing');
        console.log('Auth store isValid:', pb.authStore.isValid);

        if (!isAuthenticated) {
          console.log('User not authenticated, redirecting to login...');
          window.location.href = '/admin/login';
          return;
        }

        // Try to refresh auth token
        console.log('Attempting to refresh auth token...');
        const refreshed = await AuthManager.refresh();
        console.log('Token refresh result:', refreshed);

        if (!refreshed) {
          console.log('Token refresh failed, redirecting to login...');
          window.location.href = '/admin/login';
          return;
        }

        console.log('Authentication successful, showing admin interface...');

        // Show admin interface
        loadingScreen?.classList.add('hidden');
        adminInterface?.classList.remove('hidden');

        // Initialize the dashboard
        initializeDashboard();

      } catch (error) {
        console.error('Auth check failed:', error);
        window.location.href = '/admin/login';
      }
    });

    function initializeDashboard() {
      const user = AuthManager.getCurrentUser();
      
      // Update user info
      const userInfo = document.getElementById('userInfo');
      if (userInfo && user) {
        userInfo.textContent = `${user.name} (${user.role})`;
      }

      // Setup logout button
      document.getElementById('logoutButton')?.addEventListener('click', () => {
        AuthManager.logout();
        window.location.href = '/admin/login';
      });

      // Setup tabs
      setupTabs();
      
      // Load default tab
      showTab('content');
    }

    function setupTabs() {
      const tabButtons = document.querySelectorAll('.tab-button');
      
      tabButtons.forEach(button => {
        button.addEventListener('click', () => {
          const tabName = button.getAttribute('data-tab');
          if (tabName) {
            showTab(tabName);
          }
        });
      });
    }

    function showTab(tabName: string) {
      // Hide all tabs
      document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.add('hidden');
      });
      
      // Remove active state from all buttons
      document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('border-primary', 'text-primary');
        button.classList.add('border-transparent', 'text-gray-500');
      });
      
      // Show selected tab
      const selectedTab = document.getElementById(`${tabName}Tab`);
      selectedTab?.classList.remove('hidden');
      
      // Activate selected button
      const selectedButton = document.querySelector(`[data-tab="${tabName}"]`);
      selectedButton?.classList.remove('border-transparent', 'text-gray-500');
      selectedButton?.classList.add('border-primary', 'text-primary');
      
      // Load tab content
      switch (tabName) {
        case 'content':
          loadContentSections();
          break;
        case 'images':
          loadImages();
          break;
        case 'settings':
          loadSettings();
          break;
      }
    }

    async function loadContentSections() {
      const languageSelect = document.getElementById('languageSelect') as HTMLSelectElement;
      const language = languageSelect?.value as 'en' | 'fr' || 'en';
      
      try {
        const sections = await ContentManager.getAllContentSections(language);
        const contentList = document.getElementById('contentList');
        
        if (contentList) {
          contentList.innerHTML = sections.length > 0 
            ? sections.map(section => createContentSectionHTML(section)).join('')
            : '<div class="p-6 text-center text-gray-500">No content sections found. Click "Add Section" to create one.</div>';
        }
      } catch (error) {
        console.error('Error loading content sections:', error);
      }
    }

    function createContentSectionHTML(section: any) {
      return `
        <div class="p-6 hover:bg-gray-50">
          <div class="flex justify-between items-start">
            <div class="flex-1">
              <h3 class="text-lg font-medium text-gray-900">${section.section_key}</h3>
              ${section.title ? `<p class="text-sm text-gray-600 mt-1">${section.title}</p>` : ''}
              <div class="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                <span>Language: ${section.language.toUpperCase()}</span>
                <span>Updated: ${new Date(section.updated).toLocaleDateString()}</span>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${section.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                  ${section.enabled ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>
            <div class="flex space-x-2">
              <button 
                onclick="editContentSection('${section.id}')"
                class="text-primary hover:text-primary/80 text-sm font-medium"
              >
                Edit
              </button>
              <button 
                onclick="deleteContentSection('${section.id}')"
                class="text-red-600 hover:text-red-800 text-sm font-medium"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      `;
    }

    async function loadImages() {
      try {
        const images = await ContentManager.getImagesByCategory();
        const imageGrid = document.getElementById('imageGrid');

        if (imageGrid) {
          if (images.length > 0) {
            imageGrid.innerHTML = images.map(image => createImageCardHTML(image)).join('');
          } else {
            imageGrid.innerHTML = '<div class="col-span-full text-center text-gray-500">No images found. Click "Upload Image" to add one.</div>';
          }
        }
      } catch (error) {
        console.error('Error loading images:', error);
        const imageGrid = document.getElementById('imageGrid');
        if (imageGrid) {
          imageGrid.innerHTML = '<div class="col-span-full text-center text-red-500">Error loading images</div>';
        }
      }
    }

    function createImageCardHTML(image: any) {
      const imageUrl = ContentManager.getImageUrl(image, '200x200');
      return `
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div class="aspect-square bg-gray-100">
            <img
              src="${imageUrl}"
              alt="${image.alt_text || image.image_key}"
              class="w-full h-full object-cover"
              onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik04MCA2MEgxMjBWMTQwSDgwVjYwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K'"
            />
          </div>
          <div class="p-4">
            <h3 class="font-medium text-gray-900 text-sm truncate">${image.image_key}</h3>
            <p class="text-xs text-gray-500 mt-1">${image.category || 'general'}</p>
            <div class="mt-3 flex justify-between items-center">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${image.active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                ${image.active ? 'Active' : 'Inactive'}
              </span>
              <div class="flex space-x-1">
                <button
                  onclick="editImage('${image.id}')"
                  class="text-primary hover:text-primary/80 text-xs font-medium"
                >
                  Edit
                </button>
                <button
                  onclick="deleteImage('${image.id}')"
                  class="text-red-600 hover:text-red-800 text-xs font-medium"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      `;
    }

    function loadSettings() {
      // Load settings
      const pocketbaseUrl = document.getElementById('pocketbaseUrl');
      const userDetails = document.getElementById('userDetails');
      const user = AuthManager.getCurrentUser();
      
      if (pocketbaseUrl) {
        pocketbaseUrl.textContent = import.meta.env.PUBLIC_POCKETBASE_URL || 'http://127.0.0.1:8090';
      }
      
      if (userDetails && user) {
        userDetails.innerHTML = `
          <div class="space-y-2">
            <p><strong>Name:</strong> ${user.name}</p>
            <p><strong>Email:</strong> ${user.email}</p>
            <p><strong>Role:</strong> ${user.role}</p>
            <p><strong>Status:</strong> ${user.active ? 'Active' : 'Inactive'}</p>
          </div>
        `;
      }
    }

    // Global functions for content management
    (window as any).editContentSection = async function(id: string) {
      try {
        // Get the content section data
        const sections = await ContentManager.getAllContentSections('en');
        const section = sections.find(s => s.id === id);

        if (!section) {
          Notification.show('Content section not found', 'error');
          return;
        }

        showContentEditModal(section);
      } catch (error) {
        console.error('Error loading content section:', error);
        Notification.show('Error loading content section', 'error');
      }
    };

    (window as any).deleteContentSection = async function(id: string) {
      if (confirm('Are you sure you want to delete this content section?')) {
        try {
          // Implementation for delete
          Notification.show('Delete functionality coming soon', 'info');
        } catch (error) {
          console.error('Error deleting content section:', error);
          Notification.show('Error deleting content section', 'error');
        }
      }
    };

    // Content editing modal
    function showContentEditModal(section: any = null) {
      const modal = new Modal();
      const isEdit = !!section;

      const form = new FormBuilder()
        .addField({
          type: 'text',
          name: 'section_key',
          label: 'Section Key',
          value: section?.section_key || '',
          required: true,
          placeholder: 'e.g., home_banner, about_intro'
        })
        .addField({
          type: 'select',
          name: 'language',
          label: 'Language',
          value: section?.language || 'en',
          required: true,
          options: [
            { value: 'en', label: 'English' },
            { value: 'fr', label: 'French' }
          ]
        })
        .addField({
          type: 'text',
          name: 'title',
          label: 'Title',
          value: section?.title || '',
          placeholder: 'Section title'
        })
        .addField({
          type: 'text',
          name: 'subtitle',
          label: 'Subtitle',
          value: section?.subtitle || '',
          placeholder: 'Section subtitle'
        })
        .addField({
          type: 'textarea',
          name: 'content',
          label: 'Content',
          value: section?.content || '',
          rows: 6,
          placeholder: 'Section content (HTML allowed)'
        })
        .addField({
          type: 'textarea',
          name: 'data',
          label: 'Additional Data (JSON)',
          value: section?.data ? JSON.stringify(section.data, null, 2) : '',
          rows: 8,
          placeholder: '{"key": "value"}'
        })
        .addField({
          type: 'checkbox',
          name: 'enabled',
          label: 'Enabled',
          value: section?.enabled !== false
        })
        .addField({
          type: 'text',
          name: 'sort_order',
          label: 'Sort Order',
          value: section?.sort_order || '0',
          placeholder: '0'
        });

      if (isEdit) {
        form.addField({
          type: 'hidden',
          name: 'id',
          label: '',
          value: section.id
        });
      }

      const formHTML = form.build(isEdit ? 'Update Section' : 'Create Section', async (formData) => {
        await saveContentSection(formData, modal);
      });

      modal.show(
        isEdit ? 'Edit Content Section' : 'Add Content Section',
        formHTML,
        { size: 'lg' }
      );
    }

    // Save content section
    async function saveContentSection(formData: FormData, modal: Modal) {
      try {
        const data: any = {
          section_key: formData.get('section_key'),
          language: formData.get('language'),
          title: formData.get('title') || null,
          subtitle: formData.get('subtitle') || null,
          content: formData.get('content') || null,
          enabled: formData.get('enabled') === 'true',
          sort_order: parseInt(formData.get('sort_order') as string) || 0
        };

        // Parse JSON data if provided
        const jsonData = formData.get('data') as string;
        if (jsonData && jsonData.trim()) {
          try {
            data.data = JSON.parse(jsonData);
          } catch (e) {
            Notification.show('Invalid JSON in Additional Data field', 'error');
            return;
          }
        }

        // Add ID if editing
        const id = formData.get('id') as string;
        if (id) {
          data.id = id;
        }

        const result = await ContentManager.saveContentSection(data);

        if (result) {
          Notification.show(
            id ? 'Content section updated successfully' : 'Content section created successfully',
            'success'
          );
          modal.hide();
          loadContentSections(); // Reload the list
        } else {
          Notification.show('Error saving content section', 'error');
        }
      } catch (error) {
        console.error('Error saving content section:', error);
        Notification.show('Error saving content section', 'error');
      }
    }

    // Image management functions
    (window as any).editImage = async function(id: string) {
      try {
        const images = await ContentManager.getImagesByCategory();
        const image = images.find(img => img.id === id);

        if (!image) {
          Notification.show('Image not found', 'error');
          return;
        }

        showImageEditModal(image);
      } catch (error) {
        console.error('Error loading image:', error);
        Notification.show('Error loading image', 'error');
      }
    };

    (window as any).deleteImage = async function(id: string) {
      if (confirm('Are you sure you want to delete this image?')) {
        try {
          Notification.show('Delete functionality coming soon', 'info');
        } catch (error) {
          console.error('Error deleting image:', error);
          Notification.show('Error deleting image', 'error');
        }
      }
    };

    // Image upload modal
    function showImageUploadModal() {
      const modal = new Modal();

      const form = new FormBuilder()
        .addField({
          type: 'text',
          name: 'image_key',
          label: 'Image Key',
          required: true,
          placeholder: 'e.g., hero_background, team_photo_1'
        })
        .addField({
          type: 'file',
          name: 'file',
          label: 'Image File',
          required: true
        })
        .addField({
          type: 'text',
          name: 'alt_text',
          label: 'Alt Text',
          placeholder: 'Descriptive text for accessibility'
        })
        .addField({
          type: 'select',
          name: 'category',
          label: 'Category',
          options: [
            { value: 'general', label: 'General' },
            { value: 'banner', label: 'Banner' },
            { value: 'about', label: 'About' },
            { value: 'team', label: 'Team' },
            { value: 'service', label: 'Service' }
          ]
        })
        .addField({
          type: 'textarea',
          name: 'description',
          label: 'Description',
          rows: 3,
          placeholder: 'Optional description'
        })
        .addField({
          type: 'checkbox',
          name: 'active',
          label: 'Active',
          value: true
        });

      const formHTML = form.build('Upload Image', async (formData) => {
        await uploadImage(formData, modal);
      });

      modal.show('Upload New Image', formHTML, { size: 'lg' });
    }

    // Image edit modal
    function showImageEditModal(image: any) {
      const modal = new Modal();

      const form = new FormBuilder()
        .addField({
          type: 'hidden',
          name: 'id',
          label: '',
          value: image.id
        })
        .addField({
          type: 'text',
          name: 'image_key',
          label: 'Image Key',
          value: image.image_key,
          required: true
        })
        .addField({
          type: 'file',
          name: 'file',
          label: 'Replace Image File (optional)'
        })
        .addField({
          type: 'text',
          name: 'alt_text',
          label: 'Alt Text',
          value: image.alt_text || ''
        })
        .addField({
          type: 'select',
          name: 'category',
          label: 'Category',
          value: image.category || 'general',
          options: [
            { value: 'general', label: 'General' },
            { value: 'banner', label: 'Banner' },
            { value: 'about', label: 'About' },
            { value: 'team', label: 'Team' },
            { value: 'service', label: 'Service' }
          ]
        })
        .addField({
          type: 'textarea',
          name: 'description',
          label: 'Description',
          value: image.description || '',
          rows: 3
        })
        .addField({
          type: 'checkbox',
          name: 'active',
          label: 'Active',
          value: image.active !== false
        });

      const formHTML = form.build('Update Image', async (formData) => {
        await updateImage(formData, modal);
      });

      modal.show('Edit Image', formHTML, { size: 'lg' });
    }

    // Upload image function
    async function uploadImage(formData: FormData, modal: Modal) {
      try {
        const file = formData.get('file') as File;

        if (!file || file.size === 0) {
          Notification.show('Please select an image file', 'error');
          return;
        }

        // Validate file type
        if (!file.type.startsWith('image/')) {
          Notification.show('Please select a valid image file', 'error');
          return;
        }

        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
          Notification.show('Image file must be less than 5MB', 'error');
          return;
        }

        const data = {
          image_key: formData.get('image_key') as string,
          alt_text: formData.get('alt_text') as string || null,
          category: formData.get('category') as string || 'general',
          description: formData.get('description') as string || null,
          active: formData.get('active') === 'true'
        };

        const result = await ContentManager.saveImage(data, file);

        if (result) {
          Notification.show('Image uploaded successfully', 'success');
          modal.hide();
          loadImages(); // Reload the grid
        } else {
          Notification.show('Error uploading image', 'error');
        }
      } catch (error) {
        console.error('Error uploading image:', error);
        Notification.show('Error uploading image', 'error');
      }
    }

    // Update image function
    async function updateImage(formData: FormData, modal: Modal) {
      try {
        const file = formData.get('file') as File;
        const id = formData.get('id') as string;

        // Validate file if provided
        if (file && file.size > 0) {
          if (!file.type.startsWith('image/')) {
            Notification.show('Please select a valid image file', 'error');
            return;
          }

          if (file.size > 5 * 1024 * 1024) {
            Notification.show('Image file must be less than 5MB', 'error');
            return;
          }
        }

        const data = {
          id,
          image_key: formData.get('image_key') as string,
          alt_text: formData.get('alt_text') as string,
          category: formData.get('category') as string || 'general',
          description: formData.get('description') as string,
          active: formData.get('active') === 'true'
        };

        const result = await ContentManager.saveImage(data, file && file.size > 0 ? file : undefined);

        if (result) {
          Notification.show('Image updated successfully', 'success');
          modal.hide();
          loadImages(); // Reload the grid
        } else {
          Notification.show('Error updating image', 'error');
        }
      } catch (error) {
        console.error('Error updating image:', error);
        Notification.show('Error updating image', 'error');
      }
    }

    // Button event handlers
    document.getElementById('addContentButton')?.addEventListener('click', () => {
      showContentEditModal();
    });

    document.getElementById('uploadImageButton')?.addEventListener('click', () => {
      showImageUploadModal();
    });

    // Language change handler
    document.getElementById('languageSelect')?.addEventListener('change', loadContentSections);
  </script>
</BaseLayout>
