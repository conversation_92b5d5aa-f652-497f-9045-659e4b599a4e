---
import BaseLayout from '../layouts/BaseLayout.astro';

// This will be handled client-side for authentication
---

<BaseLayout 
  title="PTBL Content Management - Admin Dashboard"
  description="Admin dashboard for managing PTBL website content"
>
  <!-- Loading screen -->
  <div id="loadingScreen" class="fixed inset-0 bg-white z-50 flex items-center justify-center">
    <div class="text-center">
      <div class="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mb-4"></div>
      <p class="text-gray-600">Loading admin dashboard...</p>
    </div>
  </div>

  <!-- Main admin interface -->
  <div id="adminInterface" class="hidden min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-4">
          <div class="flex items-center">
            <h1 class="text-2xl font-bold text-gray-900">PTBL Content Management</h1>
          </div>
          <div class="flex items-center space-x-4">
            <span id="userInfo" class="text-sm text-gray-600"></span>
            <button 
              id="logoutButton"
              class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
    </header>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Navigation Tabs -->
      <div class="border-b border-gray-200 mb-8">
        <nav class="-mb-px flex space-x-8">
          <button 
            class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
            data-tab="content"
          >
            Content Sections
          </button>
          <button
            class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
            data-tab="images"
          >
            Images
          </button>
          <button
            class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
            data-tab="enquiries"
          >
            Enquiries
          </button>
          <button
            class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
            data-tab="settings"
          >
            Settings
          </button>
        </nav>
      </div>

      <!-- Content Sections Tab -->
      <div id="contentTab" class="tab-content hidden">
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
              <h2 class="text-lg font-medium text-gray-900">Content Sections</h2>
              <div class="flex space-x-2">
                <select id="languageSelect" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                  <option value="en">English</option>
                  <option value="fr">French</option>
                </select>
                <button 
                  id="addContentButton"
                  class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Add Section
                </button>
              </div>
            </div>
          </div>
          <div id="contentList" class="divide-y divide-gray-200">
            <!-- Content sections will be loaded here -->
          </div>
        </div>
      </div>

      <!-- Images Tab -->
      <div id="imagesTab" class="tab-content hidden">
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
              <h2 class="text-lg font-medium text-gray-900">Site Images</h2>
              <button 
                id="uploadImageButton"
                class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Upload Image
              </button>
            </div>
          </div>
          <div id="imageGrid" class="p-6 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            <!-- Images will be loaded here -->
          </div>
        </div>
      </div>

      <!-- Enquiries Tab -->
      <div id="enquiriesTab" class="tab-content hidden">
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
              <h2 class="text-lg font-medium text-gray-900">Contact Enquiries</h2>
              <div class="flex space-x-2">
                <select id="statusFilter" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                  <option value="">All Status</option>
                  <option value="new">New</option>
                  <option value="in_progress">In Progress</option>
                  <option value="resolved">Resolved</option>
                  <option value="closed">Closed</option>
                </select>
                <button
                  id="refreshEnquiriesButton"
                  class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Refresh
                </button>
              </div>
            </div>
          </div>
          <div id="enquiriesList" class="divide-y divide-gray-200">
            <!-- Enquiries will be loaded here -->
          </div>
        </div>
      </div>

      <!-- Settings Tab -->
      <div id="settingsTab" class="tab-content hidden">
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Settings</h2>
          </div>
          <div class="p-6">
            <div class="space-y-6">
              <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">PocketBase Configuration</h3>
                <div class="bg-gray-50 p-4 rounded-md">
                  <p class="text-sm text-gray-600 mb-2">PocketBase URL:</p>
                  <code class="text-sm bg-white px-2 py-1 rounded border" id="pocketbaseUrl"></code>
                </div>
              </div>
              
              <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">User Information</h3>
                <div id="userDetails" class="bg-gray-50 p-4 rounded-md">
                  <!-- User details will be loaded here -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modals will be inserted here by JavaScript -->
  <div id="modalContainer"></div>

  <script>
    import { AuthManager, ContentManager } from '../lib/pocketbase.ts';
    import pb from '../lib/pocketbase.ts';
    import { Modal, FormBuilder, Notification } from '../components/admin/Modal.ts';

    // Check authentication on page load
    document.addEventListener('DOMContentLoaded', async () => {
      const loadingScreen = document.getElementById('loadingScreen');
      const adminInterface = document.getElementById('adminInterface');

      console.log('Admin dashboard loading...');
      console.log('localStorage auth data:', localStorage.getItem('pocketbase_auth'));

      try {
        // Check if user is authenticated
        const isAuthenticated = AuthManager.isAuthenticated();
        console.log('Authentication status:', isAuthenticated);
        console.log('Auth store token:', pb.authStore.token ? 'Present' : 'Missing');
        console.log('Auth store record:', pb.authStore.record ? 'Present' : 'Missing');
        console.log('Auth store isValid:', pb.authStore.isValid);

        if (!isAuthenticated) {
          console.log('User not authenticated, redirecting to login...');
          window.location.href = '/admin/login';
          return;
        }

        // Try to refresh auth token
        console.log('Attempting to refresh auth token...');
        const refreshed = await AuthManager.refresh();
        console.log('Token refresh result:', refreshed);

        if (!refreshed) {
          console.log('Token refresh failed, redirecting to login...');
          window.location.href = '/admin/login';
          return;
        }

        console.log('Authentication successful, showing admin interface...');

        // Show admin interface
        loadingScreen?.classList.add('hidden');
        adminInterface?.classList.remove('hidden');

        // Initialize the dashboard
        initializeDashboard();

      } catch (error) {
        console.error('Auth check failed:', error);
        window.location.href = '/admin/login';
      }
    });

    function initializeDashboard() {
      const user = AuthManager.getCurrentUser();
      
      // Update user info
      const userInfo = document.getElementById('userInfo');
      if (userInfo && user) {
        userInfo.textContent = `${user.name} (${user.role})`;
      }

      // Setup logout button
      document.getElementById('logoutButton')?.addEventListener('click', () => {
        AuthManager.logout();
        window.location.href = '/admin/login';
      });

      // Setup tabs
      setupTabs();
      
      // Load default tab
      showTab('content');
    }

    function setupTabs() {
      const tabButtons = document.querySelectorAll('.tab-button');
      
      tabButtons.forEach(button => {
        button.addEventListener('click', () => {
          const tabName = button.getAttribute('data-tab');
          if (tabName) {
            showTab(tabName);
          }
        });
      });
    }

    function showTab(tabName: string) {
      // Hide all tabs
      document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.add('hidden');
      });
      
      // Remove active state from all buttons
      document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('border-primary', 'text-primary');
        button.classList.add('border-transparent', 'text-gray-500');
      });
      
      // Show selected tab
      const selectedTab = document.getElementById(`${tabName}Tab`);
      selectedTab?.classList.remove('hidden');
      
      // Activate selected button
      const selectedButton = document.querySelector(`[data-tab="${tabName}"]`);
      selectedButton?.classList.remove('border-transparent', 'text-gray-500');
      selectedButton?.classList.add('border-primary', 'text-primary');
      
      // Load tab content
      switch (tabName) {
        case 'content':
          loadContentSections();
          break;
        case 'images':
          loadImages();
          break;
        case 'enquiries':
          loadEnquiries();
          break;
        case 'settings':
          loadSettings();
          break;
      }
    }

    async function loadContentSections() {
      const languageSelect = document.getElementById('languageSelect') as HTMLSelectElement;
      const language = languageSelect?.value as 'en' | 'fr' || 'en';
      
      try {
        const sections = await ContentManager.getAllContentSections(language);
        const contentList = document.getElementById('contentList');
        
        if (contentList) {
          contentList.innerHTML = sections.length > 0 
            ? sections.map(section => createContentSectionHTML(section)).join('')
            : '<div class="p-6 text-center text-gray-500">No content sections found. Click "Add Section" to create one.</div>';
        }
      } catch (error) {
        console.error('Error loading content sections:', error);
      }
    }

    function createContentSectionHTML(section: any) {
      return `
        <div class="p-6 hover:bg-gray-50">
          <div class="flex justify-between items-start">
            <div class="flex-1">
              <h3 class="text-lg font-medium text-gray-900">${section.section_key}</h3>
              ${section.title ? `<p class="text-sm text-gray-600 mt-1">${section.title}</p>` : ''}
              <div class="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                <span>Language: ${section.language.toUpperCase()}</span>
                <span>Updated: ${new Date(section.updated).toLocaleDateString()}</span>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${section.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                  ${section.enabled ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>
            <div class="flex space-x-2">
              <button 
                onclick="editContentSection('${section.id}')"
                class="text-primary hover:text-primary/80 text-sm font-medium"
              >
                Edit
              </button>
              <button 
                onclick="deleteContentSection('${section.id}')"
                class="text-red-600 hover:text-red-800 text-sm font-medium"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      `;
    }

    async function loadImages() {
      try {
        const images = await ContentManager.getImagesByCategory();
        const imageGrid = document.getElementById('imageGrid');

        if (imageGrid) {
          if (images.length > 0) {
            imageGrid.innerHTML = images.map(image => createImageCardHTML(image)).join('');
          } else {
            imageGrid.innerHTML = '<div class="col-span-full text-center text-gray-500">No images found. Click "Upload Image" to add one.</div>';
          }
        }
      } catch (error) {
        console.error('Error loading images:', error);
        const imageGrid = document.getElementById('imageGrid');
        if (imageGrid) {
          imageGrid.innerHTML = '<div class="col-span-full text-center text-red-500">Error loading images</div>';
        }
      }
    }

    function createImageCardHTML(image: any) {
      const imageUrl = ContentManager.getImageUrl(image, '200x200');
      return `
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div class="aspect-square bg-gray-100">
            <img
              src="${imageUrl}"
              alt="${image.alt_text || image.image_key}"
              class="w-full h-full object-cover"
              onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik04MCA2MEgxMjBWMTQwSDgwVjYwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K'"
            />
          </div>
          <div class="p-4">
            <h3 class="font-medium text-gray-900 text-sm truncate">${image.image_key}</h3>
            <p class="text-xs text-gray-500 mt-1">${image.category || 'general'}</p>
            <div class="mt-3 flex justify-between items-center">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${image.active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                ${image.active ? 'Active' : 'Inactive'}
              </span>
              <div class="flex space-x-1">
                <button
                  onclick="editImage('${image.id}')"
                  class="text-primary hover:text-primary/80 text-xs font-medium"
                >
                  Edit
                </button>
                <button
                  onclick="deleteImage('${image.id}')"
                  class="text-red-600 hover:text-red-800 text-xs font-medium"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      `;
    }

    function loadSettings() {
      // Load settings
      const pocketbaseUrl = document.getElementById('pocketbaseUrl');
      const userDetails = document.getElementById('userDetails');
      const user = AuthManager.getCurrentUser();

      if (pocketbaseUrl) {
        pocketbaseUrl.textContent = import.meta.env.PUBLIC_POCKETBASE_URL || 'http://127.0.0.1:8090';
      }

      if (userDetails && user) {
        userDetails.innerHTML = `
          <div class="space-y-2">
            <p><strong>Name:</strong> ${user.name}</p>
            <p><strong>Email:</strong> ${user.email}</p>
            <p><strong>Role:</strong> ${user.role}</p>
            <p><strong>Status:</strong> ${user.active ? 'Active' : 'Inactive'}</p>
          </div>
        `;
      }
    }

    async function loadEnquiries() {
      const statusFilter = document.getElementById('statusFilter') as HTMLSelectElement;
      const status = statusFilter?.value || '';

      try {
        const enquiries = await getEnquiries(status);
        const enquiriesList = document.getElementById('enquiriesList');

        if (enquiriesList) {
          enquiriesList.innerHTML = enquiries.length > 0
            ? enquiries.map(enquiry => createEnquiryHTML(enquiry)).join('')
            : '<div class="p-6 text-center text-gray-500">No enquiries found.</div>';
        }

        // Setup refresh button
        const refreshButton = document.getElementById('refreshEnquiriesButton');
        refreshButton?.addEventListener('click', loadEnquiries);

        // Setup status filter
        statusFilter?.addEventListener('change', loadEnquiries);
      } catch (error) {
        console.error('Error loading enquiries:', error);
        const enquiriesList = document.getElementById('enquiriesList');
        if (enquiriesList) {
          enquiriesList.innerHTML = '<div class="p-6 text-center text-red-500">Error loading enquiries</div>';
        }
      }
    }

    async function getEnquiries(status: string = '') {
      try {
        let url = `${pb.baseUrl}/api/collections/enquiries/records?sort=-created&perPage=50`;
        if (status) {
          url += `&filter=status="${status}"`;
        }

        const response = await fetch(url, {
          headers: {
            'Authorization': `Bearer ${pb.authStore.token}`
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data.items || [];
      } catch (error) {
        console.error('Error fetching enquiries:', error);
        throw error;
      }
    }

    function createEnquiryHTML(enquiry: any) {
      const statusConfig = {
        new: { bg: 'bg-blue-100', text: 'text-blue-800', icon: '🆕', border: 'border-blue-200' },
        in_progress: { bg: 'bg-yellow-100', text: 'text-yellow-800', icon: '⏳', border: 'border-yellow-200' },
        resolved: { bg: 'bg-green-100', text: 'text-green-800', icon: '✅', border: 'border-green-200' },
        closed: { bg: 'bg-gray-100', text: 'text-gray-800', icon: '🔒', border: 'border-gray-200' }
      };

      const priorityConfig = {
        low: { bg: 'bg-gray-100', text: 'text-gray-700', icon: '⬇️' },
        medium: { bg: 'bg-blue-100', text: 'text-blue-700', icon: '➡️' },
        high: { bg: 'bg-orange-100', text: 'text-orange-700', icon: '⬆️' },
        urgent: { bg: 'bg-red-100', text: 'text-red-700', icon: '🚨' }
      };

      const status = statusConfig[enquiry.status] || statusConfig.new;
      const priority = enquiry.priority ? priorityConfig[enquiry.priority] || priorityConfig.medium : null;

      // Format date nicely
      const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffDays === 0) {
          return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
        } else if (diffDays === 1) {
          return `Yesterday at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
        } else if (diffDays < 7) {
          return `${diffDays} days ago`;
        } else {
          return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
        }
      };

      return `
        <div class="enquiry-card group relative bg-white border ${status.border} rounded-lg p-5 hover:border-blue-300">
          <!-- Priority indicator line -->
          ${priority && enquiry.priority === 'urgent' ? '<div class="absolute top-0 left-0 right-0 h-1 bg-red-500 rounded-t-lg priority-urgent"></div>' : ''}
          ${priority && enquiry.priority === 'high' ? '<div class="absolute top-0 left-0 right-0 h-1 bg-orange-500 rounded-t-lg"></div>' : ''}

          <div class="flex justify-between items-start">
            <div class="flex-1 min-w-0">
              <!-- Header with subject and badges -->
              <div class="flex items-start justify-between mb-3">
                <div class="flex-1 min-w-0">
                  <h3 class="text-lg font-semibold text-gray-900 truncate group-hover:text-blue-700 transition-colors duration-200">
                    ${enquiry.subject}
                  </h3>
                  <div class="flex items-center space-x-2 mt-1">
                    <span class="status-badge inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${status.bg} ${status.text}">
                      <span class="mr-1">${status.icon}</span>
                      ${enquiry.status.replace('_', ' ').toUpperCase()}
                    </span>
                    ${priority ? `
                    <span class="status-badge inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${priority.bg} ${priority.text}">
                      <span class="mr-1">${priority.icon}</span>
                      ${enquiry.priority.toUpperCase()}
                    </span>
                    ` : ''}
                  </div>
                </div>
                <div class="text-right text-xs text-gray-500 ml-4">
                  <div class="font-medium">${formatDate(enquiry.created)}</div>
                  <div class="text-gray-400">ID: ${enquiry.id.substring(0, 8)}...</div>
                </div>
              </div>

              <!-- Contact info -->
              <div class="space-y-2 mb-3">
                <div class="flex items-center space-x-2 text-sm">
                  <svg class="w-4 h-4 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                  <span class="font-medium text-gray-700">${enquiry.name}</span>
                  <span class="text-gray-500">•</span>
                  <a href="mailto:${enquiry.email}" class="text-blue-600 hover:text-blue-800 hover:underline">${enquiry.email}</a>
                  ${enquiry.phone ? `
                  <span class="text-gray-500">•</span>
                  <a href="tel:${enquiry.phone}" class="text-blue-600 hover:text-blue-800 hover:underline">${enquiry.phone}</a>
                  ` : ''}
                </div>
              </div>

              <!-- Message preview -->
              <div class="bg-gray-50 rounded-lg p-3 mb-3">
                <p class="text-sm text-gray-700 line-clamp-2">
                  ${enquiry.message.length > 120 ? enquiry.message.substring(0, 120) + '...' : enquiry.message}
                </p>
              </div>

              <!-- Footer with timestamps -->
              <div class="flex items-center justify-between text-xs text-gray-500">
                <div class="flex items-center space-x-4">
                  <span class="flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Received ${formatDate(enquiry.created)}
                  </span>
                  ${enquiry.updated !== enquiry.created ? `
                  <span class="flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Updated ${formatDate(enquiry.updated)}
                  </span>
                  ` : ''}
                </div>
              </div>
            </div>

            <!-- Action buttons -->
            <div class="flex flex-col space-y-2 ml-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <button
                onclick="viewEnquiry('${enquiry.id}')"
                class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                title="View full details"
              >
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                View
              </button>
              <button
                onclick="updateEnquiryStatus('${enquiry.id}', '${enquiry.status}')"
                class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                title="Update status and priority"
              >
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Update
              </button>
            </div>
          </div>
        </div>
      `;
    }

    // Global functions for content management
    (window as any).editContentSection = async function(id: string) {
      try {
        // Get the content section data
        const sections = await ContentManager.getAllContentSections('en');
        const section = sections.find(s => s.id === id);

        if (!section) {
          Notification.show('Content section not found', 'error');
          return;
        }

        showContentEditModal(section);
      } catch (error) {
        console.error('Error loading content section:', error);
        Notification.show('Error loading content section', 'error');
      }
    };

    (window as any).deleteContentSection = async function(id: string) {
      if (confirm('Are you sure you want to delete this content section?')) {
        try {
          // Implementation for delete
          Notification.show('Delete functionality coming soon', 'info');
        } catch (error) {
          console.error('Error deleting content section:', error);
          Notification.show('Error deleting content section', 'error');
        }
      }
    };

    // Content editing modal
    function showContentEditModal(section: any = null) {
      const modal = new Modal();
      const isEdit = !!section;

      const form = new FormBuilder()
        .addField({
          type: 'text',
          name: 'section_key',
          label: 'Section Key',
          value: section?.section_key || '',
          required: true,
          placeholder: 'e.g., home_banner, about_intro'
        })
        .addField({
          type: 'select',
          name: 'language',
          label: 'Language',
          value: section?.language || 'en',
          required: true,
          options: [
            { value: 'en', label: 'English' },
            { value: 'fr', label: 'French' }
          ]
        })
        .addField({
          type: 'text',
          name: 'title',
          label: 'Title',
          value: section?.title || '',
          placeholder: 'Section title'
        })
        .addField({
          type: 'text',
          name: 'subtitle',
          label: 'Subtitle',
          value: section?.subtitle || '',
          placeholder: 'Section subtitle'
        })
        .addField({
          type: 'textarea',
          name: 'content',
          label: 'Content',
          value: section?.content || '',
          rows: 6,
          placeholder: 'Section content (HTML allowed)'
        })
        .addField({
          type: 'textarea',
          name: 'data',
          label: 'Additional Data (JSON)',
          value: section?.data ? JSON.stringify(section.data, null, 2) : '',
          rows: 8,
          placeholder: '{"key": "value"}'
        })
        .addField({
          type: 'checkbox',
          name: 'enabled',
          label: 'Enabled',
          value: section?.enabled !== false
        })
        .addField({
          type: 'text',
          name: 'sort_order',
          label: 'Sort Order',
          value: section?.sort_order || '0',
          placeholder: '0'
        });

      if (isEdit) {
        form.addField({
          type: 'hidden',
          name: 'id',
          label: '',
          value: section.id
        });
      }

      const formHTML = form.build(isEdit ? 'Update Section' : 'Create Section', async (formData) => {
        await saveContentSection(formData, modal);
      });

      modal.show(
        isEdit ? 'Edit Content Section' : 'Add Content Section',
        formHTML,
        { size: 'lg' }
      );
    }

    // Save content section
    async function saveContentSection(formData: FormData, modal: Modal) {
      try {
        const data: any = {
          section_key: formData.get('section_key'),
          language: formData.get('language'),
          title: formData.get('title') || null,
          subtitle: formData.get('subtitle') || null,
          content: formData.get('content') || null,
          enabled: formData.get('enabled') === 'true',
          sort_order: parseInt(formData.get('sort_order') as string) || 0
        };

        // Parse JSON data if provided
        const jsonData = formData.get('data') as string;
        if (jsonData && jsonData.trim()) {
          try {
            data.data = JSON.parse(jsonData);
          } catch (e) {
            Notification.show('Invalid JSON in Additional Data field', 'error');
            return;
          }
        }

        // Add ID if editing
        const id = formData.get('id') as string;
        if (id) {
          data.id = id;
        }

        const result = await ContentManager.saveContentSection(data);

        if (result) {
          Notification.show(
            id ? 'Content section updated successfully' : 'Content section created successfully',
            'success'
          );
          modal.hide();
          loadContentSections(); // Reload the list
        } else {
          Notification.show('Error saving content section', 'error');
        }
      } catch (error) {
        console.error('Error saving content section:', error);
        Notification.show('Error saving content section', 'error');
      }
    }

    // Image management functions
    (window as any).editImage = async function(id: string) {
      try {
        const images = await ContentManager.getImagesByCategory();
        const image = images.find(img => img.id === id);

        if (!image) {
          Notification.show('Image not found', 'error');
          return;
        }

        showImageEditModal(image);
      } catch (error) {
        console.error('Error loading image:', error);
        Notification.show('Error loading image', 'error');
      }
    };

    (window as any).deleteImage = async function(id: string) {
      if (confirm('Are you sure you want to delete this image?')) {
        try {
          Notification.show('Delete functionality coming soon', 'info');
        } catch (error) {
          console.error('Error deleting image:', error);
          Notification.show('Error deleting image', 'error');
        }
      }
    };

    // Enquiry management functions
    (window as any).viewEnquiry = async function(id: string) {
      try {
        const enquiry = await getEnquiryById(id);
        if (!enquiry) {
          Notification.show('Enquiry not found', 'error');
          return;
        }
        showEnquiryViewModal(enquiry);
      } catch (error) {
        console.error('Error loading enquiry:', error);
        Notification.show('Error loading enquiry', 'error');
      }
    };

    (window as any).updateEnquiryStatus = async function(id: string, currentStatus: string) {
      try {
        const enquiry = await getEnquiryById(id);
        if (!enquiry) {
          Notification.show('Enquiry not found', 'error');
          return;
        }
        showEnquiryUpdateModal(enquiry);
      } catch (error) {
        console.error('Error loading enquiry:', error);
        Notification.show('Error loading enquiry', 'error');
      }
    };

    async function getEnquiryById(id: string) {
      try {
        const response = await fetch(`${pb.baseUrl}/api/collections/enquiries/records/${id}`, {
          headers: {
            'Authorization': `Bearer ${pb.authStore.token}`
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error fetching enquiry:', error);
        throw error;
      }
    }

    function showEnquiryViewModal(enquiry: any) {
      const modal = new Modal();

      // Helper function to get status styling
      const getStatusBadge = (status: string) => {
        const statusConfig = {
          new: { bg: 'bg-blue-100', text: 'text-blue-800', icon: '🆕' },
          in_progress: { bg: 'bg-yellow-100', text: 'text-yellow-800', icon: '⏳' },
          resolved: { bg: 'bg-green-100', text: 'text-green-800', icon: '✅' },
          closed: { bg: 'bg-gray-100', text: 'text-gray-800', icon: '🔒' }
        };
        const config = statusConfig[status] || statusConfig.new;
        return `<span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${config.bg} ${config.text}">
          <span class="mr-1">${config.icon}</span>
          ${status.replace('_', ' ').toUpperCase()}
        </span>`;
      };

      // Helper function to get priority styling
      const getPriorityBadge = (priority: string) => {
        const priorityConfig = {
          low: { bg: 'bg-gray-100', text: 'text-gray-700', icon: '⬇️' },
          medium: { bg: 'bg-blue-100', text: 'text-blue-700', icon: '➡️' },
          high: { bg: 'bg-orange-100', text: 'text-orange-700', icon: '⬆️' },
          urgent: { bg: 'bg-red-100', text: 'text-red-700', icon: '🚨' }
        };
        const config = priorityConfig[priority] || priorityConfig.medium;
        return `<span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${config.bg} ${config.text}">
          <span class="mr-1">${config.icon}</span>
          ${priority.toUpperCase()}
        </span>`;
      };

      // Format dates nicely
      const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffDays === 0) {
          return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
        } else if (diffDays === 1) {
          return `Yesterday at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
        } else if (diffDays < 7) {
          return `${diffDays} days ago`;
        } else {
          return date.toLocaleDateString([], {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          });
        }
      };

      const content = `
        <div class="space-y-6">
          <!-- Header Section with Status and Priority -->
          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">${enquiry.subject}</h3>
                <div class="flex items-center space-x-3">
                  ${getStatusBadge(enquiry.status)}
                  ${enquiry.priority ? getPriorityBadge(enquiry.priority) : ''}
                </div>
              </div>
              <div class="text-right text-sm text-gray-500">
                <div class="font-medium">ID: ${enquiry.id.substring(0, 8)}...</div>
                <div>${formatDate(enquiry.created)}</div>
              </div>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="bg-white border border-gray-200 rounded-lg p-4">
            <h4 class="text-md font-semibold text-gray-900 mb-3 flex items-center">
              <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
              Contact Information
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                  </div>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-700">Name</p>
                  <p class="text-sm text-gray-900">${enquiry.name}</p>
                </div>
              </div>
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-700">Email</p>
                  <p class="text-sm text-gray-900">
                    <a href="mailto:${enquiry.email}" class="text-blue-600 hover:text-blue-800 hover:underline">${enquiry.email}</a>
                  </p>
                </div>
              </div>
              ${enquiry.phone ? `
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                  </div>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-700">Phone</p>
                  <p class="text-sm text-gray-900">
                    <a href="tel:${enquiry.phone}" class="text-blue-600 hover:text-blue-800 hover:underline">${enquiry.phone}</a>
                  </p>
                </div>
              </div>
              ` : ''}
            </div>
          </div>

          <!-- Message Content -->
          <div class="bg-white border border-gray-200 rounded-lg p-4">
            <h4 class="text-md font-semibold text-gray-900 mb-3 flex items-center">
              <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
              </svg>
              Message
            </h4>
            <div class="bg-gray-50 rounded-lg p-4 border-l-4 border-blue-500">
              <p class="text-sm text-gray-900 whitespace-pre-wrap leading-relaxed">${enquiry.message}</p>
            </div>
          </div>

          <!-- Timeline and Notes -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Timeline -->
            <div class="bg-white border border-gray-200 rounded-lg p-4">
              <h4 class="text-md font-semibold text-gray-900 mb-3 flex items-center">
                <svg class="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Timeline
              </h4>
              <div class="space-y-3">
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                  </div>
                  <div class="text-sm">
                    <p class="font-medium text-gray-900">Enquiry Received</p>
                    <p class="text-gray-500">${formatDate(enquiry.created)}</p>
                  </div>
                </div>
                ${enquiry.updated !== enquiry.created ? `
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                  </div>
                  <div class="text-sm">
                    <p class="font-medium text-gray-900">Last Updated</p>
                    <p class="text-gray-500">${formatDate(enquiry.updated)}</p>
                  </div>
                </div>
                ` : ''}
              </div>
            </div>

            <!-- Notes Section -->
            ${enquiry.notes ? `
            <div class="bg-white border border-gray-200 rounded-lg p-4">
              <h4 class="text-md font-semibold text-gray-900 mb-3 flex items-center">
                <svg class="w-5 h-5 mr-2 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Admin Notes
              </h4>
              <div class="bg-yellow-50 rounded-lg p-3 border-l-4 border-yellow-400">
                <p class="text-sm text-gray-900 whitespace-pre-wrap">${enquiry.notes}</p>
              </div>
            </div>
            ` : `
            <div class="bg-white border border-gray-200 rounded-lg p-4">
              <h4 class="text-md font-semibold text-gray-900 mb-3 flex items-center">
                <svg class="w-5 h-5 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Admin Notes
              </h4>
              <div class="bg-gray-50 rounded-lg p-3 border-2 border-dashed border-gray-300 text-center">
                <p class="text-sm text-gray-500">No notes added yet</p>
              </div>
            </div>
            `}
          </div>

          <!-- Action Buttons -->
          <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              onclick="updateEnquiryStatus('${enquiry.id}', '${enquiry.status}')"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
              Update Status
            </button>
            <button
              onclick="window.open('mailto:${enquiry.email}?subject=Re: ${encodeURIComponent(enquiry.subject)}&body=Dear ${encodeURIComponent(enquiry.name)},%0A%0AThank you for your enquiry regarding: ${encodeURIComponent(enquiry.subject)}%0A%0A', '_blank')"
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              Reply via Email
            </button>
          </div>
        </div>
      `;

      modal.show(
        `<div class="flex items-center space-x-2">
          <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          </svg>
          <span>Enquiry Details</span>
        </div>`,
        content,
        { size: 'xl' }
      );
    }

    function showEnquiryUpdateModal(enquiry: any) {
      const modal = new Modal();

      const form = new FormBuilder()
        .addField({
          type: 'select',
          name: 'status',
          label: 'Status',
          value: enquiry.status,
          required: true,
          options: [
            { value: 'new', label: 'New' },
            { value: 'in_progress', label: 'In Progress' },
            { value: 'resolved', label: 'Resolved' },
            { value: 'closed', label: 'Closed' }
          ]
        })
        .addField({
          type: 'select',
          name: 'priority',
          label: 'Priority',
          value: enquiry.priority || 'medium',
          required: false,
          options: [
            { value: 'low', label: 'Low' },
            { value: 'medium', label: 'Medium' },
            { value: 'high', label: 'High' },
            { value: 'urgent', label: 'Urgent' }
          ]
        })
        .addField({
          type: 'textarea',
          name: 'notes',
          label: 'Notes',
          value: enquiry.notes || '',
          rows: 4,
          placeholder: 'Add notes about this enquiry...'
        })
        .addField({
          type: 'hidden',
          name: 'id',
          label: '',
          value: enquiry.id
        });

      const formHTML = form.build('Update Enquiry', async (formData) => {
        await updateEnquiry(formData, modal);
      });

      modal.show(
        'Update Enquiry',
        formHTML,
        { size: 'md' }
      );
    }

    async function updateEnquiry(formData: FormData, modal: Modal) {
      try {
        const id = formData.get('id') as string;
        const data = {
          status: formData.get('status'),
          priority: formData.get('priority'),
          notes: formData.get('notes') || null
        };

        const response = await fetch(`${pb.baseUrl}/api/collections/enquiries/records/${id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${pb.authStore.token}`
          },
          body: JSON.stringify(data)
        });

        if (response.ok) {
          Notification.show('Enquiry updated successfully', 'success');
          modal.hide();
          loadEnquiries(); // Reload the list
        } else {
          throw new Error('Failed to update enquiry');
        }
      } catch (error) {
        console.error('Error updating enquiry:', error);
        Notification.show('Error updating enquiry', 'error');
      }
    }

    // Image upload modal
    function showImageUploadModal() {
      const modal = new Modal();

      const form = new FormBuilder()
        .addField({
          type: 'text',
          name: 'image_key',
          label: 'Image Key',
          required: true,
          placeholder: 'e.g., hero_background, team_photo_1'
        })
        .addField({
          type: 'file',
          name: 'file',
          label: 'Image File',
          required: true
        })
        .addField({
          type: 'text',
          name: 'alt_text',
          label: 'Alt Text',
          placeholder: 'Descriptive text for accessibility'
        })
        .addField({
          type: 'select',
          name: 'category',
          label: 'Category',
          options: [
            { value: 'general', label: 'General' },
            { value: 'banner', label: 'Banner' },
            { value: 'about', label: 'About' },
            { value: 'team', label: 'Team' },
            { value: 'service', label: 'Service' }
          ]
        })
        .addField({
          type: 'textarea',
          name: 'description',
          label: 'Description',
          rows: 3,
          placeholder: 'Optional description'
        })
        .addField({
          type: 'checkbox',
          name: 'active',
          label: 'Active',
          value: true
        });

      const formHTML = form.build('Upload Image', async (formData) => {
        await uploadImage(formData, modal);
      });

      modal.show('Upload New Image', formHTML, { size: 'lg' });
    }

    // Image edit modal
    function showImageEditModal(image: any) {
      const modal = new Modal();

      const form = new FormBuilder()
        .addField({
          type: 'hidden',
          name: 'id',
          label: '',
          value: image.id
        })
        .addField({
          type: 'text',
          name: 'image_key',
          label: 'Image Key',
          value: image.image_key,
          required: true
        })
        .addField({
          type: 'file',
          name: 'file',
          label: 'Replace Image File (optional)'
        })
        .addField({
          type: 'text',
          name: 'alt_text',
          label: 'Alt Text',
          value: image.alt_text || ''
        })
        .addField({
          type: 'select',
          name: 'category',
          label: 'Category',
          value: image.category || 'general',
          options: [
            { value: 'general', label: 'General' },
            { value: 'banner', label: 'Banner' },
            { value: 'about', label: 'About' },
            { value: 'team', label: 'Team' },
            { value: 'service', label: 'Service' }
          ]
        })
        .addField({
          type: 'textarea',
          name: 'description',
          label: 'Description',
          value: image.description || '',
          rows: 3
        })
        .addField({
          type: 'checkbox',
          name: 'active',
          label: 'Active',
          value: image.active !== false
        });

      const formHTML = form.build('Update Image', async (formData) => {
        await updateImage(formData, modal);
      });

      modal.show('Edit Image', formHTML, { size: 'lg' });
    }

    // Upload image function
    async function uploadImage(formData: FormData, modal: Modal) {
      try {
        const file = formData.get('file') as File;

        if (!file || file.size === 0) {
          Notification.show('Please select an image file', 'error');
          return;
        }

        // Validate file type
        if (!file.type.startsWith('image/')) {
          Notification.show('Please select a valid image file', 'error');
          return;
        }

        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
          Notification.show('Image file must be less than 5MB', 'error');
          return;
        }

        const data = {
          image_key: formData.get('image_key') as string,
          alt_text: formData.get('alt_text') as string || null,
          category: formData.get('category') as string || 'general',
          description: formData.get('description') as string || null,
          active: formData.get('active') === 'true'
        };

        const result = await ContentManager.saveImage(data, file);

        if (result) {
          Notification.show('Image uploaded successfully', 'success');
          modal.hide();
          loadImages(); // Reload the grid
        } else {
          Notification.show('Error uploading image', 'error');
        }
      } catch (error) {
        console.error('Error uploading image:', error);
        Notification.show('Error uploading image', 'error');
      }
    }

    // Update image function
    async function updateImage(formData: FormData, modal: Modal) {
      try {
        const file = formData.get('file') as File;
        const id = formData.get('id') as string;

        // Validate file if provided
        if (file && file.size > 0) {
          if (!file.type.startsWith('image/')) {
            Notification.show('Please select a valid image file', 'error');
            return;
          }

          if (file.size > 5 * 1024 * 1024) {
            Notification.show('Image file must be less than 5MB', 'error');
            return;
          }
        }

        const data = {
          id,
          image_key: formData.get('image_key') as string,
          alt_text: formData.get('alt_text') as string,
          category: formData.get('category') as string || 'general',
          description: formData.get('description') as string,
          active: formData.get('active') === 'true'
        };

        const result = await ContentManager.saveImage(data, file && file.size > 0 ? file : undefined);

        if (result) {
          Notification.show('Image updated successfully', 'success');
          modal.hide();
          loadImages(); // Reload the grid
        } else {
          Notification.show('Error updating image', 'error');
        }
      } catch (error) {
        console.error('Error updating image:', error);
        Notification.show('Error updating image', 'error');
      }
    }

    // Button event handlers
    document.getElementById('addContentButton')?.addEventListener('click', () => {
      showContentEditModal();
    });

    document.getElementById('uploadImageButton')?.addEventListener('click', () => {
      showImageUploadModal();
    });

    // Language change handler
    document.getElementById('languageSelect')?.addEventListener('change', loadContentSections);
  </script>

  <style>
    /* Line clamp utility for text truncation */
    .line-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    /* Smooth transitions for hover effects */
    .group:hover .group-hover\:opacity-100 {
      opacity: 1;
    }

    /* Enhanced modal styling */
    .modal-content {
      max-height: 90vh;
      overflow-y: auto;
    }

    .modal-content::-webkit-scrollbar {
      width: 6px;
    }

    .modal-content::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    .modal-content::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    .modal-content::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }

    /* Enhanced enquiry cards */
    .enquiry-card {
      transition: all 0.2s ease-in-out;
    }

    .enquiry-card:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    /* Status indicator animations */
    .status-badge {
      animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: scale(0.9); }
      to { opacity: 1; transform: scale(1); }
    }

    /* Priority indicator pulse for urgent items */
    .priority-urgent {
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }
  </style>
</BaseLayout>
