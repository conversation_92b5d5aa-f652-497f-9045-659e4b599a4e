---
import BaseLayout from '../layouts/BaseLayout.astro';

// This will be handled client-side for authentication
---

<BaseLayout 
  title="PTBL Content Management - Admin Dashboard"
  description="Admin dashboard for managing PTBL website content"
>
  <!-- Loading screen -->
  <div id="loadingScreen" class="fixed inset-0 bg-white z-50 flex items-center justify-center">
    <div class="text-center">
      <div class="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mb-4"></div>
      <p class="text-gray-600">Loading admin dashboard...</p>
    </div>
  </div>

  <!-- Main admin interface -->
  <div id="adminInterface" class="hidden min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-4">
          <div class="flex items-center">
            <h1 class="text-2xl font-bold text-gray-900">PTBL Content Management</h1>
          </div>
          <div class="flex items-center space-x-4">
            <span id="userInfo" class="text-sm text-gray-600"></span>
            <button 
              id="logoutButton"
              class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
    </header>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Navigation Tabs -->
      <div class="border-b border-gray-200 mb-8">
        <nav class="-mb-px flex space-x-8">
          <button 
            class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
            data-tab="content"
          >
            Content Sections
          </button>
          <button
            class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
            data-tab="images"
          >
            Images
          </button>
          <button
            class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
            data-tab="enquiries"
          >
            Enquiries
          </button>
          <button
            class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
            data-tab="settings"
          >
            Settings
          </button>
        </nav>
      </div>

      <!-- Content Sections Tab -->
      <div id="contentTab" class="tab-content hidden">
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
              <h2 class="text-lg font-medium text-gray-900">Content Sections</h2>
              <div class="flex space-x-2">
                <select id="languageSelect" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                  <option value="en">English</option>
                  <option value="fr">French</option>
                </select>
                <button 
                  id="addContentButton"
                  class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Add Section
                </button>
              </div>
            </div>
          </div>
          <div id="contentList" class="divide-y divide-gray-200">
            <!-- Content sections will be loaded here -->
          </div>
        </div>
      </div>

      <!-- Images Tab -->
      <div id="imagesTab" class="tab-content hidden">
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
              <h2 class="text-lg font-medium text-gray-900">Site Images</h2>
              <button 
                id="uploadImageButton"
                class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Upload Image
              </button>
            </div>
          </div>
          <div id="imageGrid" class="p-6 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            <!-- Images will be loaded here -->
          </div>
        </div>
      </div>

      <!-- Enquiries Tab -->
      <div id="enquiriesTab" class="tab-content hidden">
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
              <h2 class="text-lg font-medium text-gray-900">Contact Enquiries</h2>
              <div class="flex space-x-2">
                <select id="statusFilter" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                  <option value="">All Status</option>
                  <option value="new">New</option>
                  <option value="in_progress">In Progress</option>
                  <option value="resolved">Resolved</option>
                  <option value="closed">Closed</option>
                </select>
                <button
                  id="refreshEnquiriesButton"
                  class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Refresh
                </button>
              </div>
            </div>
          </div>
          <div id="enquiriesList" class="divide-y divide-gray-200">
            <!-- Enquiries will be loaded here -->
          </div>
        </div>
      </div>

      <!-- Settings Tab -->
      <div id="settingsTab" class="tab-content hidden">
        <div class="space-y-6">
          <!-- System Information -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
              <div class="flex items-center space-x-2">
                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h2 class="text-lg font-medium text-gray-900">System Information</h2>
              </div>
            </div>
            <div id="systemInfo" class="p-6">
              <!-- System info will be loaded here -->
            </div>
          </div>

          <!-- User Profile -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                  <h2 class="text-lg font-medium text-gray-900">User Profile</h2>
                </div>
                <button
                  id="editProfileButton"
                  class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                  </svg>
                  Edit Profile
                </button>
              </div>
            </div>
            <div id="userProfile" class="p-6">
              <!-- User profile will be loaded here -->
            </div>
          </div>

          <!-- Database Statistics -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                  </svg>
                  <h2 class="text-lg font-medium text-gray-900">Database Statistics</h2>
                </div>
                <button
                  id="refreshStatsButton"
                  class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                  Refresh
                </button>
              </div>
            </div>
            <div id="databaseStats" class="p-6">
              <!-- Database stats will be loaded here -->
            </div>
          </div>

          <!-- Security & Access -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
              <div class="flex items-center space-x-2">
                <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
                <h2 class="text-lg font-medium text-gray-900">Security & Access</h2>
              </div>
            </div>
            <div id="securitySettings" class="p-6">
              <!-- Security settings will be loaded here -->
            </div>
          </div>

          <!-- Application Settings -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
              <div class="flex items-center space-x-2">
                <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <h2 class="text-lg font-medium text-gray-900">Application Settings</h2>
              </div>
            </div>
            <div id="appSettings" class="p-6">
              <!-- App settings will be loaded here -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modals will be inserted here by JavaScript -->
  <div id="modalContainer"></div>

  <script>
    import { AuthManager, ContentManager } from '../lib/pocketbase.ts';
    import pb from '../lib/pocketbase.ts';
    import { Modal, FormBuilder, Notification } from '../components/admin/Modal.ts';

    // Check authentication on page load
    document.addEventListener('DOMContentLoaded', async () => {
      const loadingScreen = document.getElementById('loadingScreen');
      const adminInterface = document.getElementById('adminInterface');

      console.log('Admin dashboard loading...');
      console.log('localStorage auth data:', localStorage.getItem('pocketbase_auth'));

      try {
        // Check if user is authenticated
        const isAuthenticated = AuthManager.isAuthenticated();
        console.log('Authentication status:', isAuthenticated);
        console.log('Auth store token:', pb.authStore.token ? 'Present' : 'Missing');
        console.log('Auth store record:', pb.authStore.record ? 'Present' : 'Missing');
        console.log('Auth store isValid:', pb.authStore.isValid);

        if (!isAuthenticated) {
          console.log('User not authenticated, redirecting to login...');
          window.location.href = '/admin/login';
          return;
        }

        // Try to refresh auth token
        console.log('Attempting to refresh auth token...');
        const refreshed = await AuthManager.refresh();
        console.log('Token refresh result:', refreshed);

        if (!refreshed) {
          console.log('Token refresh failed, redirecting to login...');
          window.location.href = '/admin/login';
          return;
        }

        console.log('Authentication successful, showing admin interface...');

        // Show admin interface
        loadingScreen?.classList.add('hidden');
        adminInterface?.classList.remove('hidden');

        // Initialize the dashboard
        initializeDashboard();

      } catch (error) {
        console.error('Auth check failed:', error);
        window.location.href = '/admin/login';
      }
    });

    function initializeDashboard() {
      const user = AuthManager.getCurrentUser();
      
      // Update user info
      const userInfo = document.getElementById('userInfo');
      if (userInfo && user) {
        userInfo.textContent = `${user.name} (${user.role})`;
      }

      // Setup logout button
      document.getElementById('logoutButton')?.addEventListener('click', () => {
        AuthManager.logout();
        window.location.href = '/admin/login';
      });

      // Setup tabs
      setupTabs();
      
      // Load default tab
      showTab('content');
    }

    function setupTabs() {
      const tabButtons = document.querySelectorAll('.tab-button');
      
      tabButtons.forEach(button => {
        button.addEventListener('click', () => {
          const tabName = button.getAttribute('data-tab');
          if (tabName) {
            showTab(tabName);
          }
        });
      });
    }

    function showTab(tabName: string) {
      // Hide all tabs
      document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.add('hidden');
      });
      
      // Remove active state from all buttons
      document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('border-primary', 'text-primary');
        button.classList.add('border-transparent', 'text-gray-500');
      });
      
      // Show selected tab
      const selectedTab = document.getElementById(`${tabName}Tab`);
      selectedTab?.classList.remove('hidden');
      
      // Activate selected button
      const selectedButton = document.querySelector(`[data-tab="${tabName}"]`);
      selectedButton?.classList.remove('border-transparent', 'text-gray-500');
      selectedButton?.classList.add('border-primary', 'text-primary');
      
      // Load tab content
      switch (tabName) {
        case 'content':
          loadContentSections();
          break;
        case 'images':
          loadImages();
          break;
        case 'enquiries':
          loadEnquiries();
          break;
        case 'settings':
          loadSettings();
          break;
      }
    }

    async function loadContentSections() {
      const languageSelect = document.getElementById('languageSelect') as HTMLSelectElement;
      const language = languageSelect?.value as 'en' | 'fr' || 'en';
      
      try {
        const sections = await ContentManager.getAllContentSections(language);
        const contentList = document.getElementById('contentList');
        
        if (contentList) {
          contentList.innerHTML = sections.length > 0
            ? sections.map(section => createContentSectionHTML(section)).join('')
            : `<div class="text-center py-12">
                <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                  <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No content sections found</h3>
                <p class="text-gray-500 mb-6">Get started by creating your first content section</p>
                <button
                  onclick="showContentEditModal()"
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  Add Content Section
                </button>
              </div>`;
        }
      } catch (error) {
        console.error('Error loading content sections:', error);
      }
    }

    function createContentSectionHTML(section: any) {
      // Helper function to get language styling
      const getLanguageBadge = (language: string) => {
        const langConfig = {
          en: { bg: 'bg-blue-100', text: 'text-blue-800', icon: '🇺🇸', label: 'English' },
          fr: { bg: 'bg-purple-100', text: 'text-purple-800', icon: '🇫🇷', label: 'Français' }
        };
        const config = langConfig[language] || langConfig.en;
        return `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}">
          <span class="mr-1">${config.icon}</span>
          ${config.label}
        </span>`;
      };

      // Helper function to get status styling
      const getStatusBadge = (enabled: boolean) => {
        return enabled
          ? `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              <span class="mr-1">✅</span>
              Enabled
            </span>`
          : `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              <span class="mr-1">⏸️</span>
              Disabled
            </span>`;
      };

      // Format date nicely
      const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffDays === 0) {
          return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
        } else if (diffDays === 1) {
          return `Yesterday at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
        } else if (diffDays < 7) {
          return `${diffDays} days ago`;
        } else {
          return date.toLocaleDateString([], {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
          });
        }
      };

      // Get content type icon based on section key
      const getContentIcon = (sectionKey: string) => {
        if (sectionKey.includes('banner') || sectionKey.includes('hero')) {
          return '🎯';
        } else if (sectionKey.includes('about')) {
          return '📋';
        } else if (sectionKey.includes('service')) {
          return '⚙️';
        } else if (sectionKey.includes('team')) {
          return '👥';
        } else if (sectionKey.includes('contact')) {
          return '📞';
        } else if (sectionKey.includes('faq')) {
          return '❓';
        } else if (sectionKey.includes('cta')) {
          return '🚀';
        } else {
          return '📄';
        }
      };

      // Truncate content for preview
      const getContentPreview = (content: string, maxLength: number = 100) => {
        if (!content) return 'No content';
        const stripped = content.replace(/<[^>]*>/g, ''); // Remove HTML tags
        return stripped.length > maxLength ? stripped.substring(0, maxLength) + '...' : stripped;
      };

      return `
        <div class="content-card group relative bg-white border border-gray-200 rounded-lg p-5 hover:border-blue-300 transition-all duration-200">
          <!-- Status indicator line -->
          ${!section.enabled ? '<div class="absolute top-0 left-0 right-0 h-1 bg-gray-400 rounded-t-lg"></div>' : '<div class="absolute top-0 left-0 right-0 h-1 bg-green-500 rounded-t-lg"></div>'}

          <div class="flex justify-between items-start">
            <div class="flex-1 min-w-0">
              <!-- Header with section key and badges -->
              <div class="flex items-start justify-between mb-3">
                <div class="flex-1 min-w-0">
                  <div class="flex items-center space-x-2 mb-2">
                    <span class="text-lg">${getContentIcon(section.section_key)}</span>
                    <h3 class="text-lg font-semibold text-gray-900 truncate group-hover:text-blue-700 transition-colors duration-200">
                      ${section.section_key}
                    </h3>
                  </div>
                  ${section.title ? `
                  <p class="text-sm font-medium text-gray-700 mb-2">${section.title}</p>
                  ` : ''}
                  ${section.subtitle ? `
                  <p class="text-sm text-gray-600 mb-2">${section.subtitle}</p>
                  ` : ''}
                  <div class="flex items-center space-x-2 mt-2">
                    ${getLanguageBadge(section.language)}
                    ${getStatusBadge(section.enabled)}
                    ${section.sort_order !== undefined ? `
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                      <span class="mr-1">📊</span>
                      Order: ${section.sort_order}
                    </span>
                    ` : ''}
                  </div>
                </div>
                <div class="text-right text-xs text-gray-500 ml-4">
                  <div class="font-medium">${formatDate(section.updated)}</div>
                  <div class="text-gray-400">ID: ${section.id.substring(0, 8)}...</div>
                </div>
              </div>

              <!-- Content preview -->
              ${section.content ? `
              <div class="bg-gray-50 rounded-lg p-3 mb-3">
                <div class="flex items-start space-x-2">
                  <svg class="w-4 h-4 text-gray-400 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  <p class="text-sm text-gray-700 line-clamp-2 flex-1">
                    ${getContentPreview(section.content)}
                  </p>
                </div>
              </div>
              ` : ''}

              <!-- Additional data preview -->
              ${section.data && Object.keys(section.data).length > 0 ? `
              <div class="bg-blue-50 rounded-lg p-3 mb-3">
                <div class="flex items-start space-x-2">
                  <svg class="w-4 h-4 text-blue-500 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                  </svg>
                  <div class="flex-1">
                    <p class="text-xs font-medium text-blue-700 mb-1">Additional Data</p>
                    <p class="text-xs text-blue-600">
                      ${Object.keys(section.data).length} field${Object.keys(section.data).length !== 1 ? 's' : ''}: ${Object.keys(section.data).slice(0, 3).join(', ')}${Object.keys(section.data).length > 3 ? '...' : ''}
                    </p>
                  </div>
                </div>
              </div>
              ` : ''}

              <!-- Footer with metadata -->
              <div class="flex items-center justify-between text-xs text-gray-500">
                <div class="flex items-center space-x-4">
                  <span class="flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Updated ${formatDate(section.updated)}
                  </span>
                  ${section.created !== section.updated ? `
                  <span class="flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Created ${formatDate(section.created)}
                  </span>
                  ` : ''}
                </div>
              </div>
            </div>

            <!-- Action buttons -->
            <div class="flex flex-col space-y-2 ml-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <button
                onclick="editContentSection('${section.id}')"
                class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                title="Edit content section"
              >
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit
              </button>
              <button
                onclick="duplicateContentSection('${section.id}')"
                class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                title="Duplicate content section"
              >
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
                Copy
              </button>
              <button
                onclick="deleteContentSection('${section.id}')"
                class="inline-flex items-center px-3 py-1.5 border border-red-300 text-xs font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200"
                title="Delete content section"
              >
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                Delete
              </button>
            </div>
          </div>
        </div>
      `;
    }

    async function loadImages() {
      try {
        const images = await ContentManager.getImagesByCategory();
        const imageGrid = document.getElementById('imageGrid');

        if (imageGrid) {
          if (images.length > 0) {
            imageGrid.innerHTML = images.map(image => createImageCardHTML(image)).join('');
          } else {
            imageGrid.innerHTML = '<div class="col-span-full text-center text-gray-500">No images found. Click "Upload Image" to add one.</div>';
          }
        }
      } catch (error) {
        console.error('Error loading images:', error);
        const imageGrid = document.getElementById('imageGrid');
        if (imageGrid) {
          imageGrid.innerHTML = '<div class="col-span-full text-center text-red-500">Error loading images</div>';
        }
      }
    }

    function createImageCardHTML(image: any) {
      const imageUrl = ContentManager.getImageUrl(image, '200x200');
      return `
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div class="aspect-square bg-gray-100">
            <img
              src="${imageUrl}"
              alt="${image.alt_text || image.image_key}"
              class="w-full h-full object-cover"
              onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik04MCA2MEgxMjBWMTQwSDgwVjYwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K'"
            />
          </div>
          <div class="p-4">
            <h3 class="font-medium text-gray-900 text-sm truncate">${image.image_key}</h3>
            <p class="text-xs text-gray-500 mt-1">${image.category || 'general'}</p>
            <div class="mt-3 flex justify-between items-center">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${image.active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                ${image.active ? 'Active' : 'Inactive'}
              </span>
              <div class="flex space-x-1">
                <button
                  onclick="editImage('${image.id}')"
                  class="text-primary hover:text-primary/80 text-xs font-medium"
                >
                  Edit
                </button>
                <button
                  onclick="deleteImage('${image.id}')"
                  class="text-red-600 hover:text-red-800 text-xs font-medium"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      `;
    }

    function loadSettings() {
      loadSystemInfo();
      loadUserProfile();
      loadDatabaseStats();
      loadSecuritySettings();
      loadAppSettings();

      // Setup event listeners
      setupSettingsEventListeners();
    }

    function loadSystemInfo() {
      const systemInfo = document.getElementById('systemInfo');
      const pocketbaseUrl = import.meta.env.PUBLIC_POCKETBASE_URL || 'http://127.0.0.1:8090';
      const currentTime = new Date().toLocaleString();

      if (systemInfo) {
        systemInfo.innerHTML = `
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- PocketBase Configuration -->
            <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                    </svg>
                  </div>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-blue-900">PocketBase URL</p>
                  <p class="text-xs text-blue-700 truncate">${pocketbaseUrl}</p>
                  <div class="mt-1">
                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                      <span class="w-1.5 h-1.5 bg-green-400 rounded-full mr-1"></span>
                      Connected
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- System Time -->
            <div class="bg-green-50 rounded-lg p-4 border border-green-200">
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-green-900">System Time</p>
                  <p class="text-xs text-green-700">${currentTime}</p>
                  <p class="text-xs text-green-600 mt-1">Local timezone</p>
                </div>
              </div>
            </div>

            <!-- Application Version -->
            <div class="bg-purple-50 rounded-lg p-4 border border-purple-200">
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M11 7.343V10a1 1 0 001 1h2.657"></path>
                    </svg>
                  </div>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-purple-900">PTBL Admin</p>
                  <p class="text-xs text-purple-700">Version 1.0.0</p>
                  <p class="text-xs text-purple-600 mt-1">Latest</p>
                </div>
              </div>
            </div>
          </div>
        `;
      }
    }

    function loadUserProfile() {
      const userProfile = document.getElementById('userProfile');
      const user = AuthManager.getCurrentUser();

      if (userProfile && user) {
        const formatDate = (dateString: string) => {
          return new Date(dateString).toLocaleDateString([], {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          });
        };

        userProfile.innerHTML = `
          <div class="flex items-start space-x-6">
            <!-- Avatar -->
            <div class="flex-shrink-0">
              <div class="w-20 h-20 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center">
                <span class="text-2xl font-bold text-white">${user.name.charAt(0).toUpperCase()}</span>
              </div>
            </div>

            <!-- User Details -->
            <div class="flex-1 min-w-0">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Full Name</label>
                    <p class="mt-1 text-sm text-gray-900">${user.name}</p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Email Address</label>
                    <p class="mt-1 text-sm text-gray-900">${user.email}</p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Role</label>
                    <span class="mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      ${user.role}
                    </span>
                  </div>
                </div>
                <div class="space-y-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Account Status</label>
                    <span class="mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${user.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                      <span class="w-1.5 h-1.5 ${user.active ? 'bg-green-400' : 'bg-red-400'} rounded-full mr-1"></span>
                      ${user.active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Last Login</label>
                    <p class="mt-1 text-sm text-gray-900">${formatDate(new Date().toISOString())}</p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700">User ID</label>
                    <p class="mt-1 text-xs text-gray-500 font-mono">${user.id}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        `;
      }
    }

    async function loadDatabaseStats() {
      const databaseStats = document.getElementById('databaseStats');

      if (databaseStats) {
        try {
          // Get basic stats from our collections
          const [contentSections, enquiries] = await Promise.all([
            ContentManager.getAllContentSections('en'),
            getEnquiries()
          ]);

          const stats = [
            {
              name: 'Content Sections',
              value: contentSections.length,
              icon: '📄',
              color: 'blue',
              description: 'Total content sections'
            },
            {
              name: 'Enquiries',
              value: enquiries.length,
              icon: '💬',
              color: 'green',
              description: 'Customer enquiries'
            },
            {
              name: 'Active Sections',
              value: contentSections.filter(s => s.enabled).length,
              icon: '✅',
              color: 'purple',
              description: 'Enabled content sections'
            },
            {
              name: 'Languages',
              value: [...new Set(contentSections.map(s => s.language))].length,
              icon: '🌐',
              color: 'indigo',
              description: 'Supported languages'
            }
          ];

          databaseStats.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              ${stats.map(stat => `
                <div class="bg-${stat.color}-50 rounded-lg p-4 border border-${stat.color}-200">
                  <div class="flex items-center">
                    <div class="flex-shrink-0">
                      <span class="text-2xl">${stat.icon}</span>
                    </div>
                    <div class="ml-3 flex-1">
                      <p class="text-sm font-medium text-${stat.color}-900">${stat.name}</p>
                      <p class="text-2xl font-bold text-${stat.color}-700">${stat.value}</p>
                      <p class="text-xs text-${stat.color}-600">${stat.description}</p>
                    </div>
                  </div>
                </div>
              `).join('')}
            </div>

            <div class="mt-6 bg-gray-50 rounded-lg p-4">
              <h4 class="text-sm font-medium text-gray-900 mb-3">Recent Activity</h4>
              <div class="space-y-2">
                <div class="flex items-center text-sm text-gray-600">
                  <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  Database connection healthy
                </div>
                <div class="flex items-center text-sm text-gray-600">
                  <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                  Last updated: ${new Date().toLocaleString()}
                </div>
              </div>
            </div>
          `;
        } catch (error) {
          console.error('Error loading database stats:', error);
          databaseStats.innerHTML = `
            <div class="bg-red-50 rounded-lg p-4 border border-red-200">
              <div class="flex items-center">
                <svg class="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <p class="text-sm text-red-800">Error loading database statistics</p>
              </div>
            </div>
          `;
        }
      }
    }

    function loadSecuritySettings() {
      const securitySettings = document.getElementById('securitySettings');
      const user = AuthManager.getCurrentUser();

      if (securitySettings) {
        securitySettings.innerHTML = `
          <div class="space-y-6">
            <!-- Session Information -->
            <div class="bg-green-50 rounded-lg p-4 border border-green-200">
              <h4 class="text-sm font-medium text-green-900 mb-3 flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Current Session
              </h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <p class="text-green-700 font-medium">Session Status</p>
                  <p class="text-green-600">Active & Authenticated</p>
                </div>
                <div>
                  <p class="text-green-700 font-medium">Token Expiry</p>
                  <p class="text-green-600">Auto-refresh enabled</p>
                </div>
              </div>
            </div>

            <!-- Security Actions -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="bg-white border border-gray-200 rounded-lg p-4">
                <h4 class="text-sm font-medium text-gray-900 mb-3">Password Security</h4>
                <p class="text-sm text-gray-600 mb-4">Update your account password for enhanced security.</p>
                <button
                  id="changePasswordButton"
                  class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
                  </svg>
                  Change Password
                </button>
              </div>

              <div class="bg-white border border-gray-200 rounded-lg p-4">
                <h4 class="text-sm font-medium text-gray-900 mb-3">Session Management</h4>
                <p class="text-sm text-gray-600 mb-4">Manage your active sessions and logout from all devices.</p>
                <button
                  id="logoutAllButton"
                  class="inline-flex items-center px-3 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                  </svg>
                  Logout All Sessions
                </button>
              </div>
            </div>

            <!-- Rate Limiting Status -->
            <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
              <h4 class="text-sm font-medium text-blue-900 mb-3 flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
                Rate Limiting Protection
              </h4>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <p class="text-blue-700 font-medium">Contact Form</p>
                  <p class="text-blue-600">3 submissions/hour</p>
                </div>
                <div>
                  <p class="text-blue-700 font-medium">General API</p>
                  <p class="text-blue-600">100 requests/15min</p>
                </div>
                <div>
                  <p class="text-blue-700 font-medium">Admin Access</p>
                  <p class="text-blue-600">Unlimited (Exempt)</p>
                </div>
              </div>
            </div>
          </div>
        `;
      }
    }

    function loadAppSettings() {
      const appSettings = document.getElementById('appSettings');

      if (appSettings) {
        appSettings.innerHTML = `
          <div class="space-y-6">
            <!-- Website Configuration -->
            <div class="bg-white border border-gray-200 rounded-lg p-4">
              <h4 class="text-sm font-medium text-gray-900 mb-4 flex items-center">
                <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                </svg>
                Website Configuration
              </h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Default Language</label>
                  <select id="defaultLanguage" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="en">English</option>
                    <option value="fr">Français</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Content Cache</label>
                  <button
                    id="clearCacheButton"
                    class="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Clear Cache
                  </button>
                </div>
              </div>
            </div>

            <!-- Maintenance Mode -->
            <div class="bg-white border border-gray-200 rounded-lg p-4">
              <h4 class="text-sm font-medium text-gray-900 mb-4 flex items-center">
                <svg class="w-4 h-4 mr-2 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                Maintenance Mode
              </h4>
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm text-gray-600">Enable maintenance mode to prevent public access during updates.</p>
                  <p class="text-xs text-gray-500 mt-1">Admin access will remain available.</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" id="maintenanceMode" class="sr-only peer">
                  <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>

            <!-- Backup & Export -->
            <div class="bg-white border border-gray-200 rounded-lg p-4">
              <h4 class="text-sm font-medium text-gray-900 mb-4 flex items-center">
                <svg class="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                </svg>
                Backup & Export
              </h4>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button
                  id="backupContentButton"
                  class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  Export Content
                </button>
                <button
                  id="backupImagesButton"
                  class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  Export Images
                </button>
                <button
                  id="fullBackupButton"
                  class="inline-flex items-center justify-center px-4 py-2 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                  </svg>
                  Full Backup
                </button>
              </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-gray-50 rounded-lg p-4">
              <h4 class="text-sm font-medium text-gray-900 mb-4 flex items-center">
                <svg class="w-4 h-4 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                Quick Actions
              </h4>
              <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                <a
                  href="/"
                  target="_blank"
                  class="inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-2M7 7l10 10M17 7l-10 10"></path>
                  </svg>
                  View Site
                </a>
                <a
                  href="/contact"
                  target="_blank"
                  class="inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                  </svg>
                  Contact Form
                </a>
                <button
                  id="testEmailButton"
                  class="inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                  </svg>
                  Test Email
                </button>
                <button
                  id="systemHealthButton"
                  class="inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  Health Check
                </button>
              </div>
            </div>
          </div>
        `;
      }
    }

    function setupSettingsEventListeners() {
      // Edit Profile Button
      document.getElementById('editProfileButton')?.addEventListener('click', () => {
        showEditProfileModal();
      });

      // Refresh Stats Button
      document.getElementById('refreshStatsButton')?.addEventListener('click', () => {
        loadDatabaseStats();
        Notification.show('Statistics refreshed', 'success');
      });

      // Change Password Button
      document.getElementById('changePasswordButton')?.addEventListener('click', () => {
        showChangePasswordModal();
      });

      // Logout All Sessions Button
      document.getElementById('logoutAllButton')?.addEventListener('click', () => {
        if (confirm('Are you sure you want to logout from all sessions? You will need to login again.')) {
          AuthManager.logout();
          window.location.href = '/admin/login';
        }
      });

      // Clear Cache Button
      document.getElementById('clearCacheButton')?.addEventListener('click', () => {
        // Simulate cache clearing
        Notification.show('Cache cleared successfully', 'success');
      });

      // Maintenance Mode Toggle
      document.getElementById('maintenanceMode')?.addEventListener('change', (e) => {
        const isEnabled = (e.target as HTMLInputElement).checked;
        Notification.show(
          `Maintenance mode ${isEnabled ? 'enabled' : 'disabled'}`,
          isEnabled ? 'warning' : 'success'
        );
      });

      // Backup Buttons
      document.getElementById('backupContentButton')?.addEventListener('click', () => {
        Notification.show('Content export functionality coming soon', 'info');
      });

      document.getElementById('backupImagesButton')?.addEventListener('click', () => {
        Notification.show('Image export functionality coming soon', 'info');
      });

      document.getElementById('fullBackupButton')?.addEventListener('click', () => {
        Notification.show('Full backup functionality coming soon', 'info');
      });

      // Quick Action Buttons
      document.getElementById('testEmailButton')?.addEventListener('click', () => {
        Notification.show('Email test functionality coming soon', 'info');
      });

      document.getElementById('systemHealthButton')?.addEventListener('click', () => {
        Notification.show('System health check passed ✅', 'success');
      });
    }

    function showEditProfileModal() {
      const modal = new Modal();
      const user = AuthManager.getCurrentUser();

      const form = new FormBuilder()
        .addField({
          type: 'text',
          name: 'name',
          label: 'Full Name',
          value: user?.name || '',
          required: true,
          placeholder: 'Enter your full name'
        })
        .addField({
          type: 'email',
          name: 'email',
          label: 'Email Address',
          value: user?.email || '',
          required: true,
          placeholder: 'Enter your email address'
        });

      const formHTML = form.build('Update Profile', async (formData) => {
        // Simulate profile update
        Notification.show('Profile update functionality coming soon', 'info');
        modal.hide();
      });

      modal.show(
        `<div class="flex items-center space-x-2">
          <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
          </svg>
          <span>Edit Profile</span>
        </div>`,
        formHTML,
        { size: 'md' }
      );
    }

    function showChangePasswordModal() {
      const modal = new Modal();

      const form = new FormBuilder()
        .addField({
          type: 'password',
          name: 'currentPassword',
          label: 'Current Password',
          value: '',
          required: true,
          placeholder: 'Enter your current password'
        })
        .addField({
          type: 'password',
          name: 'newPassword',
          label: 'New Password',
          value: '',
          required: true,
          placeholder: 'Enter your new password'
        })
        .addField({
          type: 'password',
          name: 'confirmPassword',
          label: 'Confirm New Password',
          value: '',
          required: true,
          placeholder: 'Confirm your new password'
        });

      const formHTML = form.build('Change Password', async (formData) => {
        const newPassword = formData.get('newPassword');
        const confirmPassword = formData.get('confirmPassword');

        if (newPassword !== confirmPassword) {
          Notification.show('Passwords do not match', 'error');
          return;
        }

        // Simulate password change
        Notification.show('Password change functionality coming soon', 'info');
        modal.hide();
      });

      modal.show(
        `<div class="flex items-center space-x-2">
          <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
          </svg>
          <span>Change Password</span>
        </div>`,
        formHTML,
        { size: 'md' }
      );
    }

    async function loadEnquiries() {
      const statusFilter = document.getElementById('statusFilter') as HTMLSelectElement;
      const status = statusFilter?.value || '';

      try {
        const enquiries = await getEnquiries(status);
        const enquiriesList = document.getElementById('enquiriesList');

        if (enquiriesList) {
          enquiriesList.innerHTML = enquiries.length > 0
            ? enquiries.map(enquiry => createEnquiryHTML(enquiry)).join('')
            : `<div class="text-center py-12">
                <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                  <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                  </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No enquiries found</h3>
                <p class="text-gray-500 mb-6">Customer enquiries will appear here when submitted through the contact form</p>
                <a
                  href="/contact"
                  target="_blank"
                  class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-2M7 7l10 10M17 7l-10 10"></path>
                  </svg>
                  View Contact Form
                </a>
              </div>`;
        }

        // Setup refresh button
        const refreshButton = document.getElementById('refreshEnquiriesButton');
        refreshButton?.addEventListener('click', loadEnquiries);

        // Setup status filter
        statusFilter?.addEventListener('change', loadEnquiries);
      } catch (error) {
        console.error('Error loading enquiries:', error);
        const enquiriesList = document.getElementById('enquiriesList');
        if (enquiriesList) {
          enquiriesList.innerHTML = '<div class="p-6 text-center text-red-500">Error loading enquiries</div>';
        }
      }
    }

    async function getEnquiries(status: string = '') {
      try {
        let url = `${pb.baseUrl}/api/collections/enquiries/records?sort=-created&perPage=50`;
        if (status) {
          url += `&filter=status="${status}"`;
        }

        const response = await fetch(url, {
          headers: {
            'Authorization': `Bearer ${pb.authStore.token}`
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data.items || [];
      } catch (error) {
        console.error('Error fetching enquiries:', error);
        throw error;
      }
    }

    function createEnquiryHTML(enquiry: any) {
      const statusConfig = {
        new: { bg: 'bg-blue-100', text: 'text-blue-800', icon: '🆕', border: 'border-blue-200' },
        in_progress: { bg: 'bg-yellow-100', text: 'text-yellow-800', icon: '⏳', border: 'border-yellow-200' },
        resolved: { bg: 'bg-green-100', text: 'text-green-800', icon: '✅', border: 'border-green-200' },
        closed: { bg: 'bg-gray-100', text: 'text-gray-800', icon: '🔒', border: 'border-gray-200' }
      };

      const priorityConfig = {
        low: { bg: 'bg-gray-100', text: 'text-gray-700', icon: '⬇️' },
        medium: { bg: 'bg-blue-100', text: 'text-blue-700', icon: '➡️' },
        high: { bg: 'bg-orange-100', text: 'text-orange-700', icon: '⬆️' },
        urgent: { bg: 'bg-red-100', text: 'text-red-700', icon: '🚨' }
      };

      const status = statusConfig[enquiry.status] || statusConfig.new;
      const priority = enquiry.priority ? priorityConfig[enquiry.priority] || priorityConfig.medium : null;

      // Format date nicely
      const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffDays === 0) {
          return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
        } else if (diffDays === 1) {
          return `Yesterday at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
        } else if (diffDays < 7) {
          return `${diffDays} days ago`;
        } else {
          return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
        }
      };

      return `
        <div class="enquiry-card group relative bg-white border ${status.border} rounded-lg p-5 hover:border-blue-300">
          <!-- Priority indicator line -->
          ${priority && enquiry.priority === 'urgent' ? '<div class="absolute top-0 left-0 right-0 h-1 bg-red-500 rounded-t-lg priority-urgent"></div>' : ''}
          ${priority && enquiry.priority === 'high' ? '<div class="absolute top-0 left-0 right-0 h-1 bg-orange-500 rounded-t-lg"></div>' : ''}

          <div class="flex justify-between items-start">
            <div class="flex-1 min-w-0">
              <!-- Header with subject and badges -->
              <div class="flex items-start justify-between mb-3">
                <div class="flex-1 min-w-0">
                  <h3 class="text-lg font-semibold text-gray-900 truncate group-hover:text-blue-700 transition-colors duration-200">
                    ${enquiry.subject}
                  </h3>
                  <div class="flex items-center space-x-2 mt-1">
                    <span class="status-badge inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${status.bg} ${status.text}">
                      <span class="mr-1">${status.icon}</span>
                      ${enquiry.status.replace('_', ' ').toUpperCase()}
                    </span>
                    ${priority ? `
                    <span class="status-badge inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${priority.bg} ${priority.text}">
                      <span class="mr-1">${priority.icon}</span>
                      ${enquiry.priority.toUpperCase()}
                    </span>
                    ` : ''}
                  </div>
                </div>
                <div class="text-right text-xs text-gray-500 ml-4">
                  <div class="font-medium">${formatDate(enquiry.created)}</div>
                  <div class="text-gray-400">ID: ${enquiry.id.substring(0, 8)}...</div>
                </div>
              </div>

              <!-- Contact info -->
              <div class="space-y-2 mb-3">
                <div class="flex items-center space-x-2 text-sm">
                  <svg class="w-4 h-4 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                  <span class="font-medium text-gray-700">${enquiry.name}</span>
                  <span class="text-gray-500">•</span>
                  <a href="mailto:${enquiry.email}" class="text-blue-600 hover:text-blue-800 hover:underline">${enquiry.email}</a>
                  ${enquiry.phone ? `
                  <span class="text-gray-500">•</span>
                  <a href="tel:${enquiry.phone}" class="text-blue-600 hover:text-blue-800 hover:underline">${enquiry.phone}</a>
                  ` : ''}
                </div>
              </div>

              <!-- Message preview -->
              <div class="bg-gray-50 rounded-lg p-3 mb-3">
                <p class="text-sm text-gray-700 line-clamp-2">
                  ${enquiry.message.length > 120 ? enquiry.message.substring(0, 120) + '...' : enquiry.message}
                </p>
              </div>

              <!-- Footer with timestamps -->
              <div class="flex items-center justify-between text-xs text-gray-500">
                <div class="flex items-center space-x-4">
                  <span class="flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Received ${formatDate(enquiry.created)}
                  </span>
                  ${enquiry.updated !== enquiry.created ? `
                  <span class="flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Updated ${formatDate(enquiry.updated)}
                  </span>
                  ` : ''}
                </div>
              </div>
            </div>

            <!-- Action buttons -->
            <div class="flex flex-col space-y-2 ml-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <button
                onclick="viewEnquiry('${enquiry.id}')"
                class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                title="View full details"
              >
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                View
              </button>
              <button
                onclick="updateEnquiryStatus('${enquiry.id}', '${enquiry.status}')"
                class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                title="Update status and priority"
              >
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Update
              </button>
            </div>
          </div>
        </div>
      `;
    }

    // Global functions for content management
    (window as any).editContentSection = async function(id: string) {
      try {
        // Get the content section data
        const sections = await ContentManager.getAllContentSections('en');
        const section = sections.find(s => s.id === id);

        if (!section) {
          Notification.show('Content section not found', 'error');
          return;
        }

        showContentEditModal(section);
      } catch (error) {
        console.error('Error loading content section:', error);
        Notification.show('Error loading content section', 'error');
      }
    };

    (window as any).duplicateContentSection = async function(id: string) {
      try {
        // Get the content section data
        const sections = await ContentManager.getAllContentSections('en');
        const section = sections.find(s => s.id === id);

        if (!section) {
          Notification.show('Content section not found', 'error');
          return;
        }

        // Create a copy with modified section_key
        const duplicatedSection = {
          ...section,
          section_key: `${section.section_key}_copy`,
          title: section.title ? `${section.title} (Copy)` : null,
          id: undefined // Remove ID so it creates a new record
        };

        showContentEditModal(duplicatedSection);
      } catch (error) {
        console.error('Error duplicating content section:', error);
        Notification.show('Error duplicating content section', 'error');
      }
    };

    (window as any).deleteContentSection = async function(id: string) {
      if (confirm('Are you sure you want to delete this content section?')) {
        try {
          // Implementation for delete
          Notification.show('Delete functionality coming soon', 'info');
        } catch (error) {
          console.error('Error deleting content section:', error);
          Notification.show('Error deleting content section', 'error');
        }
      }
    };

    // Content editing modal
    function showContentEditModal(section: any = null) {
      const modal = new Modal();
      const isEdit = !!section;

      const form = new FormBuilder()
        .addField({
          type: 'text',
          name: 'section_key',
          label: 'Section Key',
          value: section?.section_key || '',
          required: true,
          placeholder: 'e.g., home_banner, about_intro'
        })
        .addField({
          type: 'select',
          name: 'language',
          label: 'Language',
          value: section?.language || 'en',
          required: true,
          options: [
            { value: 'en', label: 'English' },
            { value: 'fr', label: 'French' }
          ]
        })
        .addField({
          type: 'text',
          name: 'title',
          label: 'Title',
          value: section?.title || '',
          placeholder: 'Section title'
        })
        .addField({
          type: 'text',
          name: 'subtitle',
          label: 'Subtitle',
          value: section?.subtitle || '',
          placeholder: 'Section subtitle'
        })
        .addField({
          type: 'textarea',
          name: 'content',
          label: 'Content',
          value: section?.content || '',
          rows: 6,
          placeholder: 'Section content (HTML allowed)'
        })
        .addField({
          type: 'textarea',
          name: 'data',
          label: 'Additional Data (JSON)',
          value: section?.data ? JSON.stringify(section.data, null, 2) : '',
          rows: 8,
          placeholder: '{"key": "value"}'
        })
        .addField({
          type: 'checkbox',
          name: 'enabled',
          label: 'Enabled',
          value: section?.enabled !== false
        })
        .addField({
          type: 'text',
          name: 'sort_order',
          label: 'Sort Order',
          value: section?.sort_order || '0',
          placeholder: '0'
        });

      if (isEdit) {
        form.addField({
          type: 'hidden',
          name: 'id',
          label: '',
          value: section.id
        });
      }

      const formHTML = form.build(isEdit ? 'Update Section' : 'Create Section', async (formData) => {
        await saveContentSection(formData, modal);
      });

      modal.show(
        isEdit ? 'Edit Content Section' : 'Add Content Section',
        formHTML,
        { size: 'lg' }
      );
    }

    // Save content section
    async function saveContentSection(formData: FormData, modal: Modal) {
      try {
        const data: any = {
          section_key: formData.get('section_key'),
          language: formData.get('language'),
          title: formData.get('title') || null,
          subtitle: formData.get('subtitle') || null,
          content: formData.get('content') || null,
          enabled: formData.get('enabled') === 'true',
          sort_order: parseInt(formData.get('sort_order') as string) || 0
        };

        // Parse JSON data if provided
        const jsonData = formData.get('data') as string;
        if (jsonData && jsonData.trim()) {
          try {
            data.data = JSON.parse(jsonData);
          } catch (e) {
            Notification.show('Invalid JSON in Additional Data field', 'error');
            return;
          }
        }

        // Add ID if editing
        const id = formData.get('id') as string;
        if (id) {
          data.id = id;
        }

        const result = await ContentManager.saveContentSection(data);

        if (result) {
          Notification.show(
            id ? 'Content section updated successfully' : 'Content section created successfully',
            'success'
          );
          modal.hide();
          loadContentSections(); // Reload the list
        } else {
          Notification.show('Error saving content section', 'error');
        }
      } catch (error) {
        console.error('Error saving content section:', error);
        Notification.show('Error saving content section', 'error');
      }
    }

    // Image management functions
    (window as any).editImage = async function(id: string) {
      try {
        const images = await ContentManager.getImagesByCategory();
        const image = images.find(img => img.id === id);

        if (!image) {
          Notification.show('Image not found', 'error');
          return;
        }

        showImageEditModal(image);
      } catch (error) {
        console.error('Error loading image:', error);
        Notification.show('Error loading image', 'error');
      }
    };

    (window as any).deleteImage = async function(id: string) {
      if (confirm('Are you sure you want to delete this image?')) {
        try {
          Notification.show('Delete functionality coming soon', 'info');
        } catch (error) {
          console.error('Error deleting image:', error);
          Notification.show('Error deleting image', 'error');
        }
      }
    };

    // Enquiry management functions
    (window as any).viewEnquiry = async function(id: string) {
      try {
        const enquiry = await getEnquiryById(id);
        if (!enquiry) {
          Notification.show('Enquiry not found', 'error');
          return;
        }
        showEnquiryViewModal(enquiry);
      } catch (error) {
        console.error('Error loading enquiry:', error);
        Notification.show('Error loading enquiry', 'error');
      }
    };

    (window as any).updateEnquiryStatus = async function(id: string, currentStatus: string) {
      try {
        const enquiry = await getEnquiryById(id);
        if (!enquiry) {
          Notification.show('Enquiry not found', 'error');
          return;
        }
        showEnquiryUpdateModal(enquiry);
      } catch (error) {
        console.error('Error loading enquiry:', error);
        Notification.show('Error loading enquiry', 'error');
      }
    };

    async function getEnquiryById(id: string) {
      try {
        const response = await fetch(`${pb.baseUrl}/api/collections/enquiries/records/${id}`, {
          headers: {
            'Authorization': `Bearer ${pb.authStore.token}`
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error fetching enquiry:', error);
        throw error;
      }
    }

    function showEnquiryViewModal(enquiry: any) {
      const modal = new Modal();

      // Helper function to get status styling
      const getStatusBadge = (status: string) => {
        const statusConfig = {
          new: { bg: 'bg-blue-100', text: 'text-blue-800', icon: '🆕' },
          in_progress: { bg: 'bg-yellow-100', text: 'text-yellow-800', icon: '⏳' },
          resolved: { bg: 'bg-green-100', text: 'text-green-800', icon: '✅' },
          closed: { bg: 'bg-gray-100', text: 'text-gray-800', icon: '🔒' }
        };
        const config = statusConfig[status] || statusConfig.new;
        return `<span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${config.bg} ${config.text}">
          <span class="mr-1">${config.icon}</span>
          ${status.replace('_', ' ').toUpperCase()}
        </span>`;
      };

      // Helper function to get priority styling
      const getPriorityBadge = (priority: string) => {
        const priorityConfig = {
          low: { bg: 'bg-gray-100', text: 'text-gray-700', icon: '⬇️' },
          medium: { bg: 'bg-blue-100', text: 'text-blue-700', icon: '➡️' },
          high: { bg: 'bg-orange-100', text: 'text-orange-700', icon: '⬆️' },
          urgent: { bg: 'bg-red-100', text: 'text-red-700', icon: '🚨' }
        };
        const config = priorityConfig[priority] || priorityConfig.medium;
        return `<span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${config.bg} ${config.text}">
          <span class="mr-1">${config.icon}</span>
          ${priority.toUpperCase()}
        </span>`;
      };

      // Format dates nicely
      const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffDays === 0) {
          return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
        } else if (diffDays === 1) {
          return `Yesterday at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
        } else if (diffDays < 7) {
          return `${diffDays} days ago`;
        } else {
          return date.toLocaleDateString([], {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          });
        }
      };

      const content = `
        <div class="space-y-6">
          <!-- Header Section with Status and Priority -->
          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">${enquiry.subject}</h3>
                <div class="flex items-center space-x-3">
                  ${getStatusBadge(enquiry.status)}
                  ${enquiry.priority ? getPriorityBadge(enquiry.priority) : ''}
                </div>
              </div>
              <div class="text-right text-sm text-gray-500">
                <div class="font-medium">ID: ${enquiry.id.substring(0, 8)}...</div>
                <div>${formatDate(enquiry.created)}</div>
              </div>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="bg-white border border-gray-200 rounded-lg p-4">
            <h4 class="text-md font-semibold text-gray-900 mb-3 flex items-center">
              <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
              Contact Information
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                  </div>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-700">Name</p>
                  <p class="text-sm text-gray-900">${enquiry.name}</p>
                </div>
              </div>
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-700">Email</p>
                  <p class="text-sm text-gray-900">
                    <a href="mailto:${enquiry.email}" class="text-blue-600 hover:text-blue-800 hover:underline">${enquiry.email}</a>
                  </p>
                </div>
              </div>
              ${enquiry.phone ? `
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                  </div>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-700">Phone</p>
                  <p class="text-sm text-gray-900">
                    <a href="tel:${enquiry.phone}" class="text-blue-600 hover:text-blue-800 hover:underline">${enquiry.phone}</a>
                  </p>
                </div>
              </div>
              ` : ''}
            </div>
          </div>

          <!-- Message Content -->
          <div class="bg-white border border-gray-200 rounded-lg p-4">
            <h4 class="text-md font-semibold text-gray-900 mb-3 flex items-center">
              <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
              </svg>
              Message
            </h4>
            <div class="bg-gray-50 rounded-lg p-4 border-l-4 border-blue-500">
              <p class="text-sm text-gray-900 whitespace-pre-wrap leading-relaxed">${enquiry.message}</p>
            </div>
          </div>

          <!-- Timeline and Notes -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Timeline -->
            <div class="bg-white border border-gray-200 rounded-lg p-4">
              <h4 class="text-md font-semibold text-gray-900 mb-3 flex items-center">
                <svg class="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Timeline
              </h4>
              <div class="space-y-3">
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                  </div>
                  <div class="text-sm">
                    <p class="font-medium text-gray-900">Enquiry Received</p>
                    <p class="text-gray-500">${formatDate(enquiry.created)}</p>
                  </div>
                </div>
                ${enquiry.updated !== enquiry.created ? `
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                  </div>
                  <div class="text-sm">
                    <p class="font-medium text-gray-900">Last Updated</p>
                    <p class="text-gray-500">${formatDate(enquiry.updated)}</p>
                  </div>
                </div>
                ` : ''}
              </div>
            </div>

            <!-- Notes Section -->
            ${enquiry.notes ? `
            <div class="bg-white border border-gray-200 rounded-lg p-4">
              <h4 class="text-md font-semibold text-gray-900 mb-3 flex items-center">
                <svg class="w-5 h-5 mr-2 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Admin Notes
              </h4>
              <div class="bg-yellow-50 rounded-lg p-3 border-l-4 border-yellow-400">
                <p class="text-sm text-gray-900 whitespace-pre-wrap">${enquiry.notes}</p>
              </div>
            </div>
            ` : `
            <div class="bg-white border border-gray-200 rounded-lg p-4">
              <h4 class="text-md font-semibold text-gray-900 mb-3 flex items-center">
                <svg class="w-5 h-5 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Admin Notes
              </h4>
              <div class="bg-gray-50 rounded-lg p-3 border-2 border-dashed border-gray-300 text-center">
                <p class="text-sm text-gray-500">No notes added yet</p>
              </div>
            </div>
            `}
          </div>

          <!-- Action Buttons -->
          <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              onclick="updateEnquiryStatus('${enquiry.id}', '${enquiry.status}')"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
              Update Status
            </button>
            <button
              onclick="window.open('mailto:${enquiry.email}?subject=Re: ${encodeURIComponent(enquiry.subject)}&body=Dear ${encodeURIComponent(enquiry.name)},%0A%0AThank you for your enquiry regarding: ${encodeURIComponent(enquiry.subject)}%0A%0A', '_blank')"
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              Reply via Email
            </button>
          </div>
        </div>
      `;

      modal.show(
        `<div class="flex items-center space-x-2">
          <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          </svg>
          <span>Enquiry Details</span>
        </div>`,
        content,
        { size: 'xl' }
      );
    }

    function showEnquiryUpdateModal(enquiry: any) {
      const modal = new Modal();

      const form = new FormBuilder()
        .addField({
          type: 'select',
          name: 'status',
          label: 'Status',
          value: enquiry.status,
          required: true,
          options: [
            { value: 'new', label: 'New' },
            { value: 'in_progress', label: 'In Progress' },
            { value: 'resolved', label: 'Resolved' },
            { value: 'closed', label: 'Closed' }
          ]
        })
        .addField({
          type: 'select',
          name: 'priority',
          label: 'Priority',
          value: enquiry.priority || 'medium',
          required: false,
          options: [
            { value: 'low', label: 'Low' },
            { value: 'medium', label: 'Medium' },
            { value: 'high', label: 'High' },
            { value: 'urgent', label: 'Urgent' }
          ]
        })
        .addField({
          type: 'textarea',
          name: 'notes',
          label: 'Notes',
          value: enquiry.notes || '',
          rows: 4,
          placeholder: 'Add notes about this enquiry...'
        })
        .addField({
          type: 'hidden',
          name: 'id',
          label: '',
          value: enquiry.id
        });

      const formHTML = form.build('Update Enquiry', async (formData) => {
        await updateEnquiry(formData, modal);
      });

      modal.show(
        'Update Enquiry',
        formHTML,
        { size: 'md' }
      );
    }

    async function updateEnquiry(formData: FormData, modal: Modal) {
      try {
        const id = formData.get('id') as string;
        const data = {
          status: formData.get('status'),
          priority: formData.get('priority'),
          notes: formData.get('notes') || null
        };

        const response = await fetch(`${pb.baseUrl}/api/collections/enquiries/records/${id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${pb.authStore.token}`
          },
          body: JSON.stringify(data)
        });

        if (response.ok) {
          Notification.show('Enquiry updated successfully', 'success');
          modal.hide();
          loadEnquiries(); // Reload the list
        } else {
          throw new Error('Failed to update enquiry');
        }
      } catch (error) {
        console.error('Error updating enquiry:', error);
        Notification.show('Error updating enquiry', 'error');
      }
    }

    // Image upload modal
    function showImageUploadModal() {
      const modal = new Modal();

      const form = new FormBuilder()
        .addField({
          type: 'text',
          name: 'image_key',
          label: 'Image Key',
          required: true,
          placeholder: 'e.g., hero_background, team_photo_1'
        })
        .addField({
          type: 'file',
          name: 'file',
          label: 'Image File',
          required: true
        })
        .addField({
          type: 'text',
          name: 'alt_text',
          label: 'Alt Text',
          placeholder: 'Descriptive text for accessibility'
        })
        .addField({
          type: 'select',
          name: 'category',
          label: 'Category',
          options: [
            { value: 'general', label: 'General' },
            { value: 'banner', label: 'Banner' },
            { value: 'about', label: 'About' },
            { value: 'team', label: 'Team' },
            { value: 'service', label: 'Service' }
          ]
        })
        .addField({
          type: 'textarea',
          name: 'description',
          label: 'Description',
          rows: 3,
          placeholder: 'Optional description'
        })
        .addField({
          type: 'checkbox',
          name: 'active',
          label: 'Active',
          value: true
        });

      const formHTML = form.build('Upload Image', async (formData) => {
        await uploadImage(formData, modal);
      });

      modal.show('Upload New Image', formHTML, { size: 'lg' });
    }

    // Image edit modal
    function showImageEditModal(image: any) {
      const modal = new Modal();

      const form = new FormBuilder()
        .addField({
          type: 'hidden',
          name: 'id',
          label: '',
          value: image.id
        })
        .addField({
          type: 'text',
          name: 'image_key',
          label: 'Image Key',
          value: image.image_key,
          required: true
        })
        .addField({
          type: 'file',
          name: 'file',
          label: 'Replace Image File (optional)'
        })
        .addField({
          type: 'text',
          name: 'alt_text',
          label: 'Alt Text',
          value: image.alt_text || ''
        })
        .addField({
          type: 'select',
          name: 'category',
          label: 'Category',
          value: image.category || 'general',
          options: [
            { value: 'general', label: 'General' },
            { value: 'banner', label: 'Banner' },
            { value: 'about', label: 'About' },
            { value: 'team', label: 'Team' },
            { value: 'service', label: 'Service' }
          ]
        })
        .addField({
          type: 'textarea',
          name: 'description',
          label: 'Description',
          value: image.description || '',
          rows: 3
        })
        .addField({
          type: 'checkbox',
          name: 'active',
          label: 'Active',
          value: image.active !== false
        });

      const formHTML = form.build('Update Image', async (formData) => {
        await updateImage(formData, modal);
      });

      modal.show('Edit Image', formHTML, { size: 'lg' });
    }

    // Upload image function
    async function uploadImage(formData: FormData, modal: Modal) {
      try {
        const file = formData.get('file') as File;

        if (!file || file.size === 0) {
          Notification.show('Please select an image file', 'error');
          return;
        }

        // Validate file type
        if (!file.type.startsWith('image/')) {
          Notification.show('Please select a valid image file', 'error');
          return;
        }

        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
          Notification.show('Image file must be less than 5MB', 'error');
          return;
        }

        const data = {
          image_key: formData.get('image_key') as string,
          alt_text: formData.get('alt_text') as string || null,
          category: formData.get('category') as string || 'general',
          description: formData.get('description') as string || null,
          active: formData.get('active') === 'true'
        };

        const result = await ContentManager.saveImage(data, file);

        if (result) {
          Notification.show('Image uploaded successfully', 'success');
          modal.hide();
          loadImages(); // Reload the grid
        } else {
          Notification.show('Error uploading image', 'error');
        }
      } catch (error) {
        console.error('Error uploading image:', error);
        Notification.show('Error uploading image', 'error');
      }
    }

    // Update image function
    async function updateImage(formData: FormData, modal: Modal) {
      try {
        const file = formData.get('file') as File;
        const id = formData.get('id') as string;

        // Validate file if provided
        if (file && file.size > 0) {
          if (!file.type.startsWith('image/')) {
            Notification.show('Please select a valid image file', 'error');
            return;
          }

          if (file.size > 5 * 1024 * 1024) {
            Notification.show('Image file must be less than 5MB', 'error');
            return;
          }
        }

        const data = {
          id,
          image_key: formData.get('image_key') as string,
          alt_text: formData.get('alt_text') as string,
          category: formData.get('category') as string || 'general',
          description: formData.get('description') as string,
          active: formData.get('active') === 'true'
        };

        const result = await ContentManager.saveImage(data, file && file.size > 0 ? file : undefined);

        if (result) {
          Notification.show('Image updated successfully', 'success');
          modal.hide();
          loadImages(); // Reload the grid
        } else {
          Notification.show('Error updating image', 'error');
        }
      } catch (error) {
        console.error('Error updating image:', error);
        Notification.show('Error updating image', 'error');
      }
    }

    // Button event handlers
    document.getElementById('addContentButton')?.addEventListener('click', () => {
      showContentEditModal();
    });

    document.getElementById('uploadImageButton')?.addEventListener('click', () => {
      showImageUploadModal();
    });

    // Language change handler
    document.getElementById('languageSelect')?.addEventListener('change', loadContentSections);
  </script>

  <style>
    /* Line clamp utility for text truncation */
    .line-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    /* Smooth transitions for hover effects */
    .group:hover .group-hover\:opacity-100 {
      opacity: 1;
    }

    /* Enhanced modal styling */
    .modal-content {
      max-height: 90vh;
      overflow-y: auto;
    }

    .modal-content::-webkit-scrollbar {
      width: 6px;
    }

    .modal-content::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    .modal-content::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    .modal-content::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }

    /* Enhanced enquiry cards */
    .enquiry-card {
      transition: all 0.2s ease-in-out;
    }

    .enquiry-card:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    /* Enhanced content section cards */
    .content-card {
      transition: all 0.2s ease-in-out;
    }

    .content-card:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    /* Status indicator animations */
    .status-badge {
      animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: scale(0.9); }
      to { opacity: 1; transform: scale(1); }
    }

    /* Priority indicator pulse for urgent items */
    .priority-urgent {
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }
  </style>
</BaseLayout>
