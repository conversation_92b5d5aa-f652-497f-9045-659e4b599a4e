---
import MainLayout from '../layouts/MainLayout.astro';
import About from '../components/sections/About.astro';
import VisionMission from '../components/sections/VisionMission.astro';
import { ServerContentFetcher } from '../lib/server-content.ts';

// Fetch about page content from database with fallbacks
const aboutData = await ServerContentFetcher.getAboutPageContent('en');
const visionMissionData = await ServerContentFetcher.getVisionMissionContent('en');
---

<MainLayout 
  title="About Us - PTBL | Power Telco Business Limited"
  description="Power Telco Business Limited (PTBL) - ECG's wholly owned subsidiary for fiber optic asset commercialization and technical services"
  lang="en"
  showPageHeader={true}
  pageTitle="About Us"
  pageSubtitle="Empowering connectivity through our fiber network infrastructure"
>
  <About
    enable={aboutData.enable}
    about_item={[{
      image: aboutData.video_thumbnail,
      title: aboutData.title,
      content: aboutData.content,
      button: aboutData.button
    }]}
  />

  <VisionMission {...visionMissionData} lang="en" />
</MainLayout>
