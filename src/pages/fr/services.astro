---
import MainLayout from '../../layouts/MainLayout.astro';
import Services from '../../components/sections/Services.astro';
import Features from '../../components/sections/Features.astro';
import { ServerContentFetcher } from '../../lib/server-content.ts';

// Fetch French services page content from database with fallbacks
const servicesData = await ServerContentFetcher.getServicesPageContent('fr');
const servicesFeaturesData = await ServerContentFetcher.getServicesFeaturesContent('fr');
---

<MainLayout 
  title="Services - PTBL | Power Telco Business Limited"
  description="Services complets de réseau de fibres incluant les Services de Ligne Louée, la Planification Réseau, l'Optimisation Réseau, et les solutions d'infrastructure à travers la zone opérationnelle d'ECG."
  lang="fr"
  showPageHeader={true}
  pageTitle="Nos Services"
  pageSubtitle="Solutions réseau complètes alimentées par l'infrastructure de fibres d'ECG"
>
  <Services {...servicesData} />
  <Features {...servicesFeaturesData} />
</MainLayout>
