---
import MainLayout from '../../layouts/MainLayout.astro';
import ContactForm from '../../components/sections/ContactForm.astro';
import { ServerContentFetcher } from '../../lib/server-content.ts';

// Fetch French contact page content from database with fallbacks
const contactData = await ServerContentFetcher.getContactPageContent('fr');

// Ensure description is a string
const description = typeof contactData.description === 'string' ? contactData.description :
  "Contactez PTBL pour des solutions fiables de réseau de fibres optiques, des services d'infrastructure et des conseils techniques. Nous fournissons des solutions de connectivité complètes à travers le Ghana.";
---

<MainLayout
  title={`${contactData.title} - PTBL | Power Telco Business Limited`}
  description={description}
  lang="fr"
  showPageHeader={true}
  pageTitle={contactData.title}
  pageSubtitle={contactData.subtitle}
>
  <ContactForm lang="fr" contactData={contactData as any} />
</MainLayout>
