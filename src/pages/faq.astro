---
import MainLayout from '../layouts/MainLayout.astro';
import FAQ from '../components/sections/FAQ.astro';
import { ServerContentFetcher } from '../lib/server-content.ts';

// Fetch FAQ page content from database with fallbacks
const faqData = await ServerContentFetcher.getFaqContent('en');
---

<MainLayout 
  title="FAQ - PTBL | Power Telco Business Limited"
  description="Frequently asked questions about PTBL's fiber network services, OPGW technology, coverage areas, and support options. Get answers to common questions."
  lang="en"
  showPageHeader={true}
  pageTitle="Frequently Asked Questions"
  pageSubtitle="Find answers to common questions about our services"
>
  <FAQ {...faqData} />
</MainLayout>
