---
import BaseLayout from '../../layouts/BaseLayout.astro';

// Server-side form handling is removed - we'll handle everything client-side
---

<BaseLayout 
  title="Admin Login - PTBL Content Management"
  description="Secure admin login for PTBL content management system"
>
  <div class="min-h-screen bg-gradient-to-br from-primary/10 to-secondary/10 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary">
          <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
          </svg>
        </div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Admin Login
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          PTBL GH Website Content Management System
        </p>
      </div>
      
      <form class="mt-8 space-y-6" id="loginForm">
        <input type="hidden" name="remember" value="true">
        <div class="rounded-md shadow-sm -space-y-px">
          <div>
            <label for="email-address" class="sr-only">Email address</label>
            <input 
              id="email-address" 
              name="email" 
              type="email" 
              autocomplete="email" 
              required 
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm" 
              placeholder="Email address"
            >
          </div>
          <div>
            <label for="password" class="sr-only">Password</label>
            <input 
              id="password" 
              name="password" 
              type="password" 
              autocomplete="current-password" 
              required 
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm" 
              placeholder="Password"
            >
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <input 
              id="remember-me" 
              name="remember-me" 
              type="checkbox" 
              class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
            >
            <label for="remember-me" class="ml-2 block text-sm text-gray-900">
              Remember me
            </label>
          </div>
        </div>

        <div>
          <button 
            type="submit" 
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-200"
            id="loginButton"
          >
            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
              <svg class="h-5 w-5 text-primary-light group-hover:text-primary-light" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
              </svg>
            </span>
            <span id="loginButtonText">Sign in</span>
          </button>
        </div>

        <!-- Error message -->
        <div id="errorMessage" class="hidden mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                Login Failed
              </h3>
              <div class="mt-2 text-sm text-red-700" id="errorText">
                Invalid email or password. Please try again.
              </div>
            </div>
          </div>
        </div>

        <!-- Success message -->
        <div id="successMessage" class="hidden mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-green-800">
                Login Successful
              </h3>
              <div class="mt-2 text-sm text-green-700">
                Redirecting to admin dashboard...
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <script>
    import { AuthManager } from '../../lib/pocketbase.ts';

    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', () => {
      // Check if already logged in
      if (AuthManager.isAuthenticated()) {
        console.log('User already authenticated, redirecting...');
        window.location.href = '/edit-ptblgh';
        return;
      }

      // Form submission handler
      const loginForm = document.getElementById('loginForm');
      if (loginForm) {
        loginForm.addEventListener('submit', async (e) => {
          e.preventDefault();

          const formData = new FormData(e.target as HTMLFormElement);
          const email = formData.get('email') as string;
          const password = formData.get('password') as string;

          console.log('Attempting login for:', email);
          await handleLogin(email, password);
        });
      }
    });

    async function handleLogin(email: string, password: string) {
      const loginButton = document.getElementById('loginButton') as HTMLButtonElement;
      const loginButtonText = document.getElementById('loginButtonText');
      const errorMessage = document.getElementById('errorMessage');
      const successMessage = document.getElementById('successMessage');

      // Show loading state
      if (loginButton) loginButton.disabled = true;
      if (loginButtonText) loginButtonText.textContent = 'Signing in...';
      errorMessage?.classList.add('hidden');
      successMessage?.classList.add('hidden');

      try {
        console.log('Calling AuthManager.login...');
        const user = await AuthManager.login(email, password);
        console.log('Login result:', user);

        if (user) {
          // Success
          console.log('Login successful, showing success message');
          successMessage?.classList.remove('hidden');

          // Verify authentication state
          const isAuth = AuthManager.isAuthenticated();
          console.log('Authentication state after login:', isAuth);

          setTimeout(() => {
            console.log('Redirecting to admin dashboard...');
            window.location.href = '/edit-ptblgh';
          }, 1000); // Reduced delay
        } else {
          // Failed
          console.log('Login failed - no user returned');
          throw new Error('Invalid credentials');
        }
      } catch (error) {
        console.error('Login error:', error);
        errorMessage?.classList.remove('hidden');

        // Update error message with more details
        const errorText = document.getElementById('errorText');
        if (errorText) {
          errorText.textContent = (error as Error).message || 'Invalid email or password. Please try again.';
        }

        // Reset button
        if (loginButton) loginButton.disabled = false;
        if (loginButtonText) loginButtonText.textContent = 'Sign in';
      }
    }
  </script>
</BaseLayout>
