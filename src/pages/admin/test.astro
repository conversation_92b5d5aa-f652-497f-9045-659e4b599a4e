---
// Test page to verify PocketBase connection and setup
---

<html>
<head>
  <title>PocketBase Test</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
  <div class="max-w-4xl mx-auto">
    <h1 class="text-3xl font-bold mb-6">PocketBase Connection Test</h1>
    
    <div class="space-y-4">
      <div class="bg-white p-4 rounded-lg shadow">
        <h2 class="text-xl font-semibold mb-2">Connection Status</h2>
        <div id="connectionStatus" class="text-gray-600">Testing...</div>
      </div>

      <div class="bg-white p-4 rounded-lg shadow">
        <h2 class="text-xl font-semibold mb-2">Collections</h2>
        <div id="collectionsStatus" class="text-gray-600">Checking...</div>
      </div>

      <div class="bg-white p-4 rounded-lg shadow">
        <h2 class="text-xl font-semibold mb-2">Admin Users</h2>
        <div id="adminUsersStatus" class="text-gray-600">Checking...</div>
      </div>

      <div class="bg-white p-4 rounded-lg shadow">
        <h2 class="text-xl font-semibold mb-2">Create Test Admin User</h2>
        <form id="createAdminForm" class="space-y-3">
          <div>
            <label class="block text-sm font-medium text-gray-700">Email</label>
            <input type="email" name="email" value="<EMAIL>" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md" required>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Password</label>
            <input type="password" name="password" value="admin123456" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md" required>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Name</label>
            <input type="text" name="name" value="Admin User" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md" required>
          </div>
          <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
            Create Admin User
          </button>
        </form>
        <div id="createAdminResult" class="mt-3"></div>
      </div>

      <div class="bg-white p-4 rounded-lg shadow">
        <h2 class="text-xl font-semibold mb-2">Test Login</h2>
        <form id="testLoginForm" class="space-y-3">
          <div>
            <label class="block text-sm font-medium text-gray-700">Email</label>
            <input type="email" name="email" value="<EMAIL>" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md" required>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Password</label>
            <input type="password" name="password" value="admin123456" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md" required>
          </div>
          <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
            Test Login
          </button>
        </form>
        <div id="testLoginResult" class="mt-3"></div>
      </div>
    </div>
  </div>

  <script type="module">
    const PB_URL = 'http://127.0.0.1:8090';

    // Test basic connection
    async function testConnection() {
      try {
        const response = await fetch(`${PB_URL}/api/health`);
        if (response.ok) {
          document.getElementById('connectionStatus').innerHTML = '<span class="text-green-600">✓ Connected to PocketBase</span>';
          return true;
        } else {
          throw new Error(`HTTP ${response.status}`);
        }
      } catch (error) {
        document.getElementById('connectionStatus').innerHTML = `<span class="text-red-600">✗ Connection failed: ${error.message}</span>`;
        return false;
      }
    }

    // Test collections
    async function testCollections() {
      try {
        const collections = ['content_sections', 'site_images', 'admin_users'];
        const results = [];
        
        for (const collection of collections) {
          try {
            const response = await fetch(`${PB_URL}/api/collections/${collection}/records?perPage=1`);
            if (response.ok) {
              results.push(`<span class="text-green-600">✓ ${collection}</span>`);
            } else {
              results.push(`<span class="text-red-600">✗ ${collection} (${response.status})</span>`);
            }
          } catch (error) {
            results.push(`<span class="text-red-600">✗ ${collection} (${error.message})</span>`);
          }
        }
        
        document.getElementById('collectionsStatus').innerHTML = results.join('<br>');
      } catch (error) {
        document.getElementById('collectionsStatus').innerHTML = `<span class="text-red-600">Error: ${error.message}</span>`;
      }
    }

    // Check admin users
    async function checkAdminUsers() {
      try {
        const response = await fetch(`${PB_URL}/api/collections/admin_users/records`);
        if (response.ok) {
          const data = await response.json();
          document.getElementById('adminUsersStatus').innerHTML = 
            `<span class="text-blue-600">Found ${data.totalItems} admin users</span>`;
        } else {
          document.getElementById('adminUsersStatus').innerHTML = 
            `<span class="text-red-600">Error checking admin users: ${response.status}</span>`;
        }
      } catch (error) {
        document.getElementById('adminUsersStatus').innerHTML = 
          `<span class="text-red-600">Error: ${error.message}</span>`;
      }
    }

    // Create admin user
    document.getElementById('createAdminForm').addEventListener('submit', async (e) => {
      e.preventDefault();
      const formData = new FormData(e.target);
      
      try {
        const response = await fetch(`${PB_URL}/api/collections/admin_users/records`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: formData.get('email'),
            password: formData.get('password'),
            passwordConfirm: formData.get('password'),
            name: formData.get('name'),
            role: 'admin',
            active: true
          })
        });

        if (response.ok) {
          const data = await response.json();
          document.getElementById('createAdminResult').innerHTML = 
            `<span class="text-green-600">✓ Admin user created successfully! ID: ${data.id}</span>`;
        } else {
          const error = await response.json();
          document.getElementById('createAdminResult').innerHTML = 
            `<span class="text-red-600">✗ Error: ${JSON.stringify(error)}</span>`;
        }
      } catch (error) {
        document.getElementById('createAdminResult').innerHTML = 
          `<span class="text-red-600">✗ Error: ${error.message}</span>`;
      }
    });

    // Test login
    document.getElementById('testLoginForm').addEventListener('submit', async (e) => {
      e.preventDefault();
      const formData = new FormData(e.target);
      
      try {
        const response = await fetch(`${PB_URL}/api/collections/admin_users/auth-with-password`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            identity: formData.get('email'),
            password: formData.get('password')
          })
        });

        if (response.ok) {
          const data = await response.json();
          document.getElementById('testLoginResult').innerHTML = 
            `<span class="text-green-600">✓ Login successful! User: ${data.record.name}</span>`;
        } else {
          const error = await response.json();
          document.getElementById('testLoginResult').innerHTML = 
            `<span class="text-red-600">✗ Login failed: ${JSON.stringify(error)}</span>`;
        }
      } catch (error) {
        document.getElementById('testLoginResult').innerHTML = 
          `<span class="text-red-600">✗ Error: ${error.message}</span>`;
      }
    });

    // Run tests on page load
    async function runTests() {
      const connected = await testConnection();
      if (connected) {
        await testCollections();
        await checkAdminUsers();
      }
    }

    runTests();
  </script>
</body>
</html>
