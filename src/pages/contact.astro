---
import MainLayout from '../layouts/MainLayout.astro';
import ContactForm from '../components/sections/ContactForm.astro';
import { ServerContentFetcher } from '../lib/server-content.ts';

// Fetch English contact page content from database with fallbacks
const contactData = await ServerContentFetcher.getContactPageContent('en');

// Ensure description is a string
const description = typeof contactData.description === 'string' ? contactData.description :
  "Get in touch with PTBL for reliable fiber optic network solutions, infrastructure services, and technical consultancy. We provide comprehensive connectivity solutions across ECG's operational area.";
---

<MainLayout
  title={`${contactData.title} - PTBL | Power Telco Business Limited`}
  description={description}
  lang="en"
  showPageHeader={true}
  pageTitle={contactData.title}
  pageSubtitle={contactData.subtitle}
>
  <ContactForm lang="en" contactData={contactData as any} />
</MainLayout>
