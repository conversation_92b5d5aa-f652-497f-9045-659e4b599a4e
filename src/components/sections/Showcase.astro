---
export interface Props {
  enable: boolean;
  subtitle: string;
  title: string;
  content: string;
  highlights?: Array<{
    icon: string;
    title: string;
    description: string;
  }>;
}

const { enable, subtitle, title, content, highlights = [] } = Astro.props;
---

{enable && (
  <section class="section bg-gradient-to-r from-primary to-secondary text-white relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 bg-triangles opacity-10"></div>
    
    <div class="container relative z-10">
      <div class="text-center max-w-4xl mx-auto">
        <p class="text-blue-200 font-semibold text-sm uppercase tracking-wider mb-4" data-aos="fade-up">
          {subtitle}
        </p>
        <h2 class="text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight mb-8" data-aos="fade-up" data-aos-delay="200">
          {title}
        </h2>
        <div class="text-xl leading-relaxed opacity-90" data-aos="fade-up" data-aos-delay="400" set:html={content}></div>
      </div>
      
      <!-- Highlights from database -->
      {highlights && highlights.length > 0 && (
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-16">
          {highlights.map((highlight, index) => (
            <div class="text-center" data-aos="fade-up" data-aos-delay={600 + (index * 100)}>
              <div class="mb-4">
                <i class={`${highlight.icon} text-4xl text-blue-200`}></i>
              </div>
              <h3 class="text-xl font-bold mb-2">{highlight.title}</h3>
              <p class="text-blue-200 text-sm">{highlight.description}</p>
            </div>
          ))}
        </div>
      )}

      <!-- Fallback stats if no highlights -->
      {(!highlights || highlights.length === 0) && (
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
          <div class="text-center" data-aos="fade-up" data-aos-delay="600">
            <div class="text-4xl font-bold mb-2">100+</div>
            <div class="text-blue-200">Network Points</div>
          </div>
          <div class="text-center" data-aos="fade-up" data-aos-delay="700">
            <div class="text-4xl font-bold mb-2">99.9%</div>
            <div class="text-blue-200">Uptime Guarantee</div>
          </div>
          <div class="text-center" data-aos="fade-up" data-aos-delay="800">
            <div class="text-4xl font-bold mb-2">24/7</div>
            <div class="text-blue-200">Support Available</div>
          </div>
        </div>
      )}
    </div>
  </section>
)}
