import { ContentManager } from './pocketbase.ts';

// Content fetching utilities with fallbacks
export class ContentFetcher {

  // Get banner content with fallback
  static async getBannerContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await ContentManager.getContentSection('home_banner', language);

      if (content && content.enabled) {
        // Parse data if it exists
        const data = content.data || {};

        return {
          enable: content.enabled,
          bg_image: data.bg_image || "images/background/hero.webp",
          title: content.title || "We Connect. You Power. The World Runs",
          subtitle: content.subtitle || "Fiber Network Excellence",
          watermark: data.watermark || "PTBL",
          content: content.content || "Empowering businesses and communities with cutting-edge fiber network infrastructure.",
          features: data.features || [
            "99.9% Network Uptime",
            "24/7 Expert Support",
            "Across ECG's operational area"
          ],
          button: data.button || {
            enable: true,
            label: "Contact Us",
            link: "/contact"
          },
          secondary_button: data.secondary_button || {
            enable: true,
            label: "View Services",
            link: "/services"
          }
        };
      }
    } catch (error) {
      console.warn('Failed to fetch banner content from PocketBase, using fallback');
    }

    // Fallback content
    return language === 'fr' ? {
      enable: true,
      bg_image: "images/background/hero.webp",
      title: "Nous Connectons. Vous Alimentez. Le Monde Fonctionne",
      subtitle: "Excellence du Réseau de Fibres",
      watermark: "PTBL",
      content: "Autonomiser les entreprises et les communautés avec une infrastructure de réseau de fibres de pointe.",
      features: [
        "99,9% de Disponibilité du Réseau",
        "Support Expert 24/7",
        "Dans la zone opérationnelle d'ECG"
      ],
      button: {
        enable: true,
        label: "Contactez-nous",
        link: "/fr/contact"
      },
      secondary_button: {
        enable: true,
        label: "Voir les Services",
        link: "/fr/services"
      }
    } : {
      enable: true,
      bg_image: "images/background/hero.webp",
      title: "We Connect. You Power. The World Runs",
      subtitle: "Fiber Network Excellence",
      watermark: "PTBL",
      content: "Empowering businesses and communities with cutting-edge fiber network infrastructure.",
      features: [
        "99.9% Network Uptime",
        "24/7 Expert Support",
        "Across ECG's operational area"
      ],
      button: {
        enable: true,
        label: "Contact Us",
        link: "/contact"
      },
      secondary_button: {
        enable: true,
        label: "View Services",
        link: "/services"
      }
    };
  }

  // Get about content with fallback
  static async getAboutContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await ContentManager.getContentSection('home_services', language);

      if (content && content.enabled) {
        const data = content.data || {};

        return {
          enable: content.enabled,
          about_item: [{
            image: "images/about/about-1.jpeg",
            // subtitle: "About PTBL",
            title: "Empowering ECG with Advanced Network Services",
            content: "We provide comprehensive fiber network services to ECG, delivering reliable Leased Line Services, strategic Network Planning, and advanced Network Optimization Services. Our solutions ensure robust and efficient network infrastructure for critical operations.",
            stats: [
              { number: "500+", label: "Network Points" },
              { number: "99.9%", label: "SLA Uptime" },
              { number: "24/7", label: "Support" }
            ],
            button: {
              enable: true,
              label: "Learn More",
              link: "/about"
            }
          },
          {
            image: "images/about/about-2.jpeg",
            // subtitle: "About PTBL",
            title: "Commercial Network Services for Businesses",
            content: "Our commercial services include professional Fiber Lease Services, secure POP Colocation Services, and convenient Pole Rental Services. We provide the infrastructure businesses need to thrive in the digital age.",
            stats: [
              { number: "500+", label: "Network Points" },
              { number: "99.9%", label: "SLA Uptime" },
              { number: "24/7", label: "Support" }
            ],
            button: {
              enable: true,
              label: "Learn More",
              link: "/services"
            }
          }]
        };
      }
    } catch (error) {
      console.warn('Failed to fetch about content from PocketBase, using fallback');
    }

    // Fallback content
    return language === 'fr' ? {
      enable: true,
      about_item: [{
        image: "/images/about/framework.png",
        title: "HISTOIRE DE L'ENTREPRISE",
        content: "Power Telco Business Limited (PTBL) est une filiale entièrement détenue d'ECG...",
        button: {
          enable: true,
          label: "En Savoir Plus",
          link: "/fr/about"
        }
      }]
    } : {
      enable: true,
      about_item: [
        {
          image: "images/about/about-1.jpeg",
          // subtitle: "About PTBL",
          title: "Empowering ECG with Advanced Network Services",
          content: "We provide comprehensive fiber network services to ECG, delivering reliable Leased Line Services, strategic Network Planning, and advanced Network Optimization Services. Our solutions ensure robust and efficient network infrastructure for critical operations.",
          stats: [
            { number: "500+", label: "Network Points" },
            { number: "99.9%", label: "SLA Uptime" },
            { number: "24/7", label: "Support" }
          ],
          button: {
            enable: true,
            label: "Learn More",
            link: "/about"
          }
        },
        {
          image: "images/about/about-2.jpeg",
          // subtitle: "About PTBL",
          title: "Commercial Network Services for Businesses",
          content: "Our commercial services include professional Fiber Lease Services, secure POP Colocation Services, and convenient Pole Rental Services. We provide the infrastructure businesses need to thrive in the digital age.",
          stats: [
            { number: "500+", label: "Network Points" },
            { number: "99.9%", label: "SLA Uptime" },
            { number: "24/7", label: "Support" }
          ],
          button: {
            enable: true,
            label: "Learn More",
            link: "/services"
          }
        }]
    };
  }

  // Get features content with fallback
  static async getFeaturesContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await ContentManager.getContentSection('network_solutions', language);

      if (content && content.enabled) {
        const data = content.data || {};

        return {
          enable: content.enabled,
          subtitle: content.subtitle || "OUR SERVICES",
          title: content.title || "Comprehensive Network Solutions",
          content: content.content || "powered by ECG's fiber infrastructure",
          feature_item: data.feature_item || []
        };
      }
    } catch (error) {
      console.warn('Failed to fetch features content from PocketBase, using fallback');
    }

    // Fallback content
    const defaultFeatures = language === 'fr' ? [
      {
        name: "Services Réseau ECG",
        image: "/images/feature/feature-1.jpeg",
        content: "Services complets de réseau de fibres incluant les Services de Ligne Louée, la Planification de Réseau, et les Services d'Optimisation de Réseau pour les opérations d'ECG.",
        icon: "fas fa-network-wired",
        color: "blue"
      },
      {
        name: "Services Réseau Commerciaux",
        image: "/images/feature/feature-2.jpeg",
        content: "En collaboration avec notre partenaire commercial, nous offrons des services professionnels de réseau de fibres incluant les Services de Location de Fibres, le Service de Colocation POP, et le Service de Location de Poteaux pour les entreprises.",
        icon: "fas fa-building",
        color: "green"
      },
      {
        name: "Services de Conseil Technique",
        image: "/images/feature/feature-3.jpeg",
        content: "Services experts de support technique et de conseil pour optimiser votre infrastructure et opérations réseau.",
        icon: "fas fa-broadcast-tower",
        color: "purple"
      }
    ] : [
      {
        name: "ECG Network Services",
        image: "/images/feature/feature-1.jpeg",
        content: "Comprehensive fiber network services including Leased Line Services, Network Planning, and Network Optimization Services for ECG operations.",
        icon: "fas fa-network-wired",
        color: "blue"
      },
      {
        name: "Commercial Network Services",
        image: "/images/feature/feature-2.jpeg",
        content: "In collaboration with our commercial partner, we offer professional fiber network services including Fiber Lease Services, POP Colocation Service, and Pole Rental Service for businesses.",
        icon: "fas fa-building",
        color: "green"
      },
      {
        name: "Technical Consultancy Services",
        image: "/images/feature/feature-3.jpeg",
        content: "Expert technical support and consultancy services to optimize your network infrastructure and operations.",
        icon: "fas fa-broadcast-tower",
        color: "purple"
      }
    ];

    return {
      enable: true,
      subtitle: language === 'fr' ? "NOS SERVICES" : "OUR SERVICES",
      title: language === 'fr' ? "Solutions Réseau Complètes" : "Comprehensive Network Solutions",
      content: language === 'fr' ? "alimentées par l'infrastructure de fibres d'ECG" : "powered by ECG's fiber infrastructure",
      feature_item: defaultFeatures
    };
  }

  // Get team content with fallback
  static async getTeamContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await ContentManager.getContentSection('team_section', language);

      if (content && content.enabled) {
        const data = content.data || {};

        return {
          enable: content.enabled,
          subtitle: content.subtitle || "OUR TEAM",
          title: content.title || "Meet Our Leadership",
          content: content.content || "Experienced professionals driving PTBL's success",
          team_member: data.team_member || []
        };
      }
    } catch (error) {
      console.warn('Failed to fetch team content from PocketBase, using fallback');
    }

    // Fallback content
    return {
      enable: true,
      subtitle: language === 'fr' ? "NOTRE ÉQUIPE" : "OUR TEAM",
      title: language === 'fr' ? "Rencontrez Notre Direction" : "Meet Our Leadership",
      content: language === 'fr' ? "Professionnels expérimentés qui dirigent le succès de PTBL" : "Experienced professionals driving PTBL's success",
      team_member: []
    };
  }

  // Get image URL with fallback
  static async getImageUrl(imageKey: string, fallbackUrl: string, thumb?: string): Promise<string> {
    try {
      const image = await ContentManager.getImage(imageKey);
      if (image) {
        return ContentManager.getImageUrl(image, thumb);
      }
    } catch (error) {
      console.warn(`Failed to fetch image ${imageKey} from PocketBase, using fallback`);
    }

    return fallbackUrl;
  }

  // Get stats content with fallback
  static async getStatsContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await ContentManager.getContentSection('home_stats', language);

      if (content && content.enabled) {
        const data = content.data || {};

        return {
          enable: content.enabled,
          stats_item: data.stats_item || []
        };
      }
    } catch (error) {
      console.warn('Failed to fetch stats content from PocketBase, using fallback');
    }

    // Fallback content
    const defaultStats = language === 'fr' ? [
      {
        icon: "fas fa-network-wired",
        number: "500+",
        label: "Kilomètres de Fibre"
      },
      {
        icon: "fas fa-building",
        number: "100+",
        label: "Entreprises Connectées"
      },
      {
        icon: "fas fa-clock",
        number: "99.9%",
        label: "Disponibilité"
      }
    ] : [
      {
        icon: "fas fa-network-wired",
        number: "500+",
        label: "Kilometers of Fiber"
      },
      {
        icon: "fas fa-building",
        number: "100+",
        label: "Connected Businesses"
      },
      {
        icon: "fas fa-clock",
        number: "99.9%",
        label: "Uptime"
      }
    ];

    return {
      enable: true,
      stats_item: defaultStats
    };
  }

  // Generic content fetcher
  static async getContent(sectionKey: string, language: 'en' | 'fr' = 'en', fallback: Record<string, unknown> = {}) {
    try {
      const content = await ContentManager.getContentSection(sectionKey, language);

      if (content && content.enabled) {
        return {
          ...fallback,
          ...content.data,
          enable: content.enabled,
          title: content.title,
          subtitle: content.subtitle,
          content: content.content
        };
      }
    } catch (error) {
      console.warn(`Failed to fetch content ${sectionKey} from PocketBase, using fallback`);
    }

    return fallback;
  }
}
