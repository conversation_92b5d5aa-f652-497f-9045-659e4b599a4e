// Rate limiting utility for protecting API endpoints
// Uses in-memory storage for simplicity - in production, consider using Redis or database

interface RateLimitEntry {
  count: number;
  resetTime: number;
}

interface RateLimitConfig {
  maxRequests: number;
  windowMs: number;
  skipSuccessfulRequests?: boolean;
}

class RateLimiter {
  private store: Map<string, RateLimitEntry> = new Map();
  private config: RateLimitConfig;

  constructor(config: RateLimitConfig) {
    this.config = config;
    
    // Clean up expired entries every 5 minutes
    setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  private cleanup() {
    const now = Date.now();
    for (const [key, entry] of this.store.entries()) {
      if (now > entry.resetTime) {
        this.store.delete(key);
      }
    }
  }

  private getClientKey(request: Request): string {
    // Try to get real IP from headers (for proxies/load balancers)
    const forwarded = request.headers.get('x-forwarded-for');
    const realIp = request.headers.get('x-real-ip');
    const cfConnectingIp = request.headers.get('cf-connecting-ip');
    
    // Use the first available IP
    const ip = forwarded?.split(',')[0]?.trim() || 
               realIp || 
               cfConnectingIp || 
               'unknown';
    
    return ip;
  }

  check(request: Request): { allowed: boolean; resetTime?: number; remaining?: number } {
    const key = this.getClientKey(request);
    const now = Date.now();
    
    let entry = this.store.get(key);
    
    // If no entry exists or the window has expired, create a new one
    if (!entry || now > entry.resetTime) {
      entry = {
        count: 0,
        resetTime: now + this.config.windowMs
      };
    }
    
    // Check if limit is exceeded
    if (entry.count >= this.config.maxRequests) {
      return {
        allowed: false,
        resetTime: entry.resetTime,
        remaining: 0
      };
    }
    
    // Increment count and update store
    entry.count++;
    this.store.set(key, entry);
    
    return {
      allowed: true,
      resetTime: entry.resetTime,
      remaining: this.config.maxRequests - entry.count
    };
  }

  // Method to record a request (for post-processing rate limiting)
  record(request: Request, success: boolean = true) {
    if (this.config.skipSuccessfulRequests && success) {
      return;
    }
    
    // This method can be used for more sophisticated rate limiting
    // For now, we handle everything in the check method
  }
}

// Pre-configured rate limiters for different endpoints
export const contactFormLimiter = new RateLimiter({
  maxRequests: 3,
  windowMs: 60 * 60 * 1000, // 1 hour
  skipSuccessfulRequests: false
});

export const generalApiLimiter = new RateLimiter({
  maxRequests: 100,
  windowMs: 15 * 60 * 1000, // 15 minutes
  skipSuccessfulRequests: false
});

// Helper function to create rate limit response
export function createRateLimitResponse(resetTime: number, message?: string) {
  const resetTimeSeconds = Math.ceil(resetTime / 1000);
  const retryAfter = Math.ceil((resetTime - Date.now()) / 1000);
  
  return new Response(JSON.stringify({
    error: 'Rate limit exceeded',
    message: message || 'Too many requests. Please try again later.',
    retryAfter: retryAfter,
    resetTime: new Date(resetTime).toISOString()
  }), {
    status: 429,
    headers: {
      'Content-Type': 'application/json',
      'Retry-After': retryAfter.toString(),
      'X-RateLimit-Reset': resetTimeSeconds.toString(),
      'X-RateLimit-Remaining': '0'
    }
  });
}

// Helper function to add rate limit headers to successful responses
export function addRateLimitHeaders(response: Response, remaining: number, resetTime: number) {
  const resetTimeSeconds = Math.ceil(resetTime / 1000);
  
  response.headers.set('X-RateLimit-Remaining', remaining.toString());
  response.headers.set('X-RateLimit-Reset', resetTimeSeconds.toString());
  
  return response;
}

export { RateLimiter };
