// API middleware for rate limiting and authentication
import type { APIRoute } from 'astro';
import { generalApiLimiter, createRateLimitResponse, addRateLimitHeaders } from './rate-limiter.ts';
import pb from './pocketbase.ts';

// Helper function to check if user is authenticated admin
export function isAuthenticatedAdmin(request: Request): boolean {
  try {
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return false;
    }

    const token = authHeader.substring(7);
    
    // Check if the token is valid and belongs to an admin
    // This is a simplified check - in production, you might want to verify the token more thoroughly
    return pb.authStore.isValid && pb.authStore.token === token;
  } catch (error) {
    console.error('Error checking admin authentication:', error);
    return false;
  }
}

// Rate limiting middleware that can be applied to any API route
export function withRateLimit(handler: APIRoute, exemptAdmins: boolean = true): APIRoute {
  return async (context) => {
    const { request } = context;

    try {
      // Skip rate limiting for authenticated admins if exemptAdmins is true
      if (exemptAdmins && isAuthenticatedAdmin(request)) {
        return await handler(context);
      }

      // Check general API rate limit
      const rateLimitResult = generalApiLimiter.check(request);
      
      if (!rateLimitResult.allowed) {
        return createRateLimitResponse(
          rateLimitResult.resetTime!,
          'API rate limit exceeded. Please wait before making more requests. Limit: 100 requests per 15 minutes.'
        );
      }

      // Execute the original handler
      const response = await handler(context);

      // Add rate limit headers to successful responses
      if (response.status < 400) {
        return addRateLimitHeaders(response, rateLimitResult.remaining!, rateLimitResult.resetTime!);
      }

      return response;

    } catch (error) {
      console.error('Rate limiting middleware error:', error);
      // If rate limiting fails, allow the request to proceed
      return await handler(context);
    }
  };
}

// Specific middleware for admin-only endpoints
export function withAdminAuth(handler: APIRoute): APIRoute {
  return async (context) => {
    const { request } = context;

    if (!isAuthenticatedAdmin(request)) {
      return new Response(JSON.stringify({
        error: 'Unauthorized',
        message: 'Admin authentication required'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    return await handler(context);
  };
}

// Combined middleware for admin endpoints with rate limiting exemption
export function withAdminRateLimit(handler: APIRoute): APIRoute {
  return withRateLimit(withAdminAuth(handler), true);
}

// Helper function to get client IP address
export function getClientIP(request: Request): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIp = request.headers.get('x-real-ip');
  const cfConnectingIp = request.headers.get('cf-connecting-ip');
  
  return forwarded?.split(',')[0]?.trim() || 
         realIp || 
         cfConnectingIp || 
         'unknown';
}

// Logging middleware for API requests
export function withLogging(handler: APIRoute, logLevel: 'info' | 'debug' = 'info'): APIRoute {
  return async (context) => {
    const { request } = context;
    const startTime = Date.now();
    const method = request.method;
    const url = new URL(request.url);
    const clientIP = getClientIP(request);

    try {
      const response = await handler(context);
      const duration = Date.now() - startTime;

      if (logLevel === 'debug' || response.status >= 400) {
        console.log(`[API] ${method} ${url.pathname} - ${response.status} - ${duration}ms - IP: ${clientIP}`);
      }

      return response;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`[API] ${method} ${url.pathname} - ERROR - ${duration}ms - IP: ${clientIP}`, error);
      throw error;
    }
  };
}

// Combined middleware for public API endpoints
export function withPublicApiMiddleware(handler: APIRoute): APIRoute {
  return withLogging(withRateLimit(handler, false), 'info');
}

// Combined middleware for admin API endpoints
export function withAdminApiMiddleware(handler: APIRoute): APIRoute {
  return withLogging(withAdminRateLimit(handler), 'debug');
}
