// Server-side content fetching for Astro components
// This runs on the server during build/SSR

interface ContentSection {
  id?: string;
  section_key: string;
  language: 'en' | 'fr';
  title?: string;
  subtitle?: string;
  content?: [];
  data?: Record<string, unknown>;
  enabled?: boolean;
  sort_order?: number;
}

interface SiteImage {
  id?: string;
  image_key: string;
  alt_text?: string | null;
  file: string;
  description?: string | null;
  category?: string;
  active?: boolean;
}

// Server-side PocketBase client
export class ServerPocketBase {
  private baseUrl: string;

  constructor() {
    this.baseUrl = import.meta.env.PUBLIC_POCKETBASE_URL || 'http://127.0.0.1:8090';
  }

  async getContentSection(sectionKey: string, language: 'en' | 'fr' = 'en'): Promise<ContentSection | null> {
    try {
      const url = `${this.baseUrl}/api/collections/content_sections/records?filter=section_key="${sectionKey}" %26%26 language="${language}"&perPage=1`;
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.items && data.items.length > 0) {
        return data.items[0] as ContentSection;
      }
      
      return null;
    } catch (error) {
      console.warn(`Failed to fetch content section ${sectionKey} (${language}):`, error);
      return null;
    }
  }

  async getImage(imageKey: string): Promise<SiteImage | null> {
    try {
      const url = `${this.baseUrl}/api/collections/site_images/records?filter=image_key="${imageKey}" %26%26 active=true&perPage=1`;
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.items && data.items.length > 0) {
        return data.items[0] as SiteImage;
      }
      
      return null;
    } catch (error) {
      console.warn(`Failed to fetch image ${imageKey}:`, error);
      return null;
    }
  }

  getImageUrl(image: SiteImage, thumb?: string): string {
    if (!image.file) return '';
    let url = `${this.baseUrl}/api/files/site_images/${image.id}/${image.file}`;
    if (thumb) {
      url += `?thumb=${thumb}`;
    }
    return url;
  }

  async createRecord(collection: string, data: Record<string, unknown>): Promise<any> {
    try {
      const url = `${this.baseUrl}/api/collections/${collection}/records`;
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`Failed to create record in ${collection}:`, error);
      throw error;
    }
  }
}

// Server content fetcher
export class ServerContentFetcher {
  private static pb = new ServerPocketBase();

  // Get banner content with fallback
  static async getBannerContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await this.pb.getContentSection('home_banner', language);
      
      if (content && content.enabled) {
        const data = content.data || {};
        
        return {
          enable: content.enabled,
          bg_image: data.bg_image || "images/background/hero.webp",
          title: content.title || "We Connect. You Power. The World Runs",
          subtitle: content.subtitle || "Fiber Network Excellence",
          watermark: data.watermark || "PTBL",
          content: content.content || "Empowering businesses and communities with cutting-edge fiber network infrastructure.",
          features: data.features || [
            "99.9% Network Uptime",
            "24/7 Expert Support",
            "Across ECG's operational area"
          ],
          button: data.button || {
            enable: true,
            label: "Contact Us",
            link: "/contact"
          },
          secondary_button: data.secondary_button || {
            enable: true,
            label: "View Services",
            link: "/services"
          }
        };
      }
    } catch (error) {
      console.warn('Failed to fetch banner content from PocketBase, using fallback');
    }

    // Return fallback content
    return this.getFallbackBannerContent(language);
  }

  // Get about content with fallback
  static async getAboutContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await this.pb.getContentSection('home_services', language);
      
      if (content && content.enabled) {
        const data = content.data || {};
        
        // If data contains about_item array, use it; otherwise create single item
        if (data.about_item && Array.isArray(data.about_item)) {
          return {
            enable: content.enabled,
            about_item: data.about_item
          };
        } else {
          return {
            enable: content.enabled,
            about_item: [{
              image: data.image || "/images/about/framework.png",
              title: content.title || "COMPANY HISTORY",
              content: content.content || "Power Telco Business Limited (PTBL) is ECG's wholly owned subsidiary established to leverage the company's extensive fiber optic infrastructure. Built on ECG's power transmission network, PTBL provides comprehensive telecommunications services across Ghana, connecting businesses and communities with reliable, high-speed fiber connectivity.",
              button: data.button || {
                enable: true,
                label: "Learn More",
                link: "/about"
              }
            }]
          };
        }
      }
    } catch (error) {
      console.warn('Failed to fetch about content from PocketBase, using fallback');
    }

    return this.getFallbackAboutContent(language);
  }

  // Get features content with fallback
  static async getFeaturesContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await this.pb.getContentSection('network_solutions', language);
      
      if (content && content.enabled) {
        const data = content.data || {};
        
        return {
          enable: content.enabled,
          subtitle: content.subtitle || "OUR SERVICES",
          title: content.title || "Comprehensive Network Solutions",
          content: content.content || "powered by ECG's fiber infrastructure",
          feature_item: data.feature_item || []
        };
      }
    } catch (error) {
      console.warn('Failed to fetch features content from PocketBase, using fallback');
    }

    return this.getFallbackFeaturesContent(language);
  }

  // Get stats content with fallback
  static async getStatsContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await this.pb.getContentSection('home_stats', language);

      if (content && content.enabled) {
        const data = content.data || {};

        return {
          enable: content.enabled,
          stats_item: data.stats_item || []
        };
      }
    } catch (error) {
      console.warn('Failed to fetch stats content from PocketBase, using fallback');
    }

    return this.getFallbackStatsContent(language);
  }

  // Get promo video content with fallback
  static async getPromoVideoContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await this.pb.getContentSection('network_coverage', language);

      if (content && content.enabled) {
        const data = content.data || {};

        return {
          enable: content.enabled,
          video_thumbnail: data.video_thumbnail || "images/fibre_coverage.jpeg",
          video_link: data.video_link || "",
          subtitle: content.subtitle || (language === 'fr' ? "Couverture Réseau" : "Network Coverage"),
          title: content.title || (language === 'fr' ? "Étendre la Portée Numérique du Ghana" : "Expanding Ghana's Digital Reach"),
          content: content.content || "",
          features: data.features || []
        };
      }
    } catch (_error) {
      console.warn('Failed to fetch promo video content from PocketBase, using fallback');
    }

    return this.getFallbackPromoVideoContent(language);
  }

  // Get showcase content with fallback
  static async getShowcaseContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await this.pb.getContentSection('home_showcase', language);

      if (content && content.enabled) {
        const data = content.data || {};

        return {
          enable: content.enabled,
          subtitle: content.subtitle || (language === 'fr' ? "Pourquoi Choisir PTBL" : "Why Choose PTBL"),
          title: content.title || (language === 'fr' ? "Permettre la Transformation Numérique du Ghana" : "Enabling Ghana's Digital Transformation"),
          content: content.content || "",
          highlights: data.highlights || []
        };
      }
    } catch (_error) {
      console.warn('Failed to fetch showcase content from PocketBase, using fallback');
    }

    return this.getFallbackShowcaseContent(language);
  }

  // Get CTA content with fallback
  static async getCtaContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await this.pb.getContentSection('home_cta', language);

      if (content && content.enabled) {
        const data = content.data || {};

        return {
          enable: content.enabled,
          bg_image: data.bg_image || "images/background/cta-bg.jpg",
          subtitle: content.subtitle || (language === 'fr' ? "Prêt à Se Connecter?" : "Ready to Connect?"),
          title: content.title || (language === 'fr' ? "Construisons Votre Solution Réseau" : "Let's Build Your Network Solution"),
          content: content.content || "",
          button: data.button || {
            enable: true,
            label: language === 'fr' ? "Consultation Gratuite" : "Get Free Consultation",
            link: language === 'fr' ? "/fr/contact" : "/contact"
          },
          secondary_button: data.secondary_button || {
            enable: true,
            label: language === 'fr' ? "Appeler +233 24-388-9991" : "Call +233 24-388-9991",
            link: "tel:+233243889991"
          }
        };
      }
    } catch (_error) {
      console.warn('Failed to fetch CTA content from PocketBase, using fallback');
    }

    return this.getFallbackCtaContent(language);
  }

  // Get footer content with fallback
  static async getFooterContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await this.pb.getContentSection('footer', language);

      if (content && content.enabled) {
        const data = content.data || {};

        return {
          enable: content.enabled,
          company: data.company || (language === 'fr' ? "Entreprise" : "Company"),
          quickLinks: data.quickLinks || (language === 'fr' ? "Liens Rapides" : "Quick Links"),
          followUs: data.followUs || (language === 'fr' ? "Suivez-nous" : "Follow Us"),
          contactInfo: data.contactInfo || (language === 'fr' ? "Informations de Contact" : "Contact Info"),
          description: content.content || "",
          address: data.address || "",
          phone: data.phone || "+233 24-388-9991",
          email: data.email || "<EMAIL>",
          copyright: data.copyright || "",
          poweredBy: data.poweredBy || (language === 'fr' ? "Propulsé par" : "Powered by"),
          links: data.links || [],
          socialLinks: data.socialLinks || []
        };
      }
    } catch (_error) {
      console.warn('Failed to fetch footer content from PocketBase, using fallback');
    }

    return this.getFallbackFooterContent(language);
  }

  // Get about page content with fallback
  static async getAboutPageContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await this.pb.getContentSection('about_page', language);

      if (content && content.enabled) {
        const data = content.data || {};

        return {
          enable: content.enabled,
          video_thumbnail: data.video_thumbnail || "/images/about/framework.png",
          title: content.title || (language === 'fr' ? "HISTOIRE DE L'ENTREPRISE" : "COMPANY HISTORY"),
          content: content.content || "",
          button: data.button || {
            enable: true,
            label: language === 'fr' ? "Contactez-nous" : "Contact Us",
            link: language === 'fr' ? "/fr/contact" : "/contact"
          }
        };
      }
    } catch (_error) {
      console.warn('Failed to fetch about page content from PocketBase, using fallback');
    }

    return this.getFallbackAboutPageContent(language);
  }

  // Get vision mission content with fallback
  static async getVisionMissionContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await this.pb.getContentSection('vision_mission', language);

      if (content && content.enabled) {
        const data = content.data || {};

        return {
          enable: content.enabled,
          items: data.items || []
        };
      }
    } catch (_error) {
      console.warn('Failed to fetch vision mission content from PocketBase, using fallback');
    }

    return this.getFallbackVisionMissionContent(language);
  }

  // Get services page content with fallback
  static async getServicesPageContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await this.pb.getContentSection('services_page', language);

      if (content && content.enabled) {
        const data = content.data || {};

        return {
          enable: content.enabled,
          subtitle: content.subtitle || (language === 'fr' ? "Nos Services" : "Our Services"),
          title: content.title || (language === 'fr' ? "Solutions Réseau Complètes" : "Comprehensive Network Solutions"),
          service_item: data.service_item || []
        };
      }
    } catch (_error) {
      console.warn('Failed to fetch services page content from PocketBase, using fallback');
    }

    return this.getFallbackServicesPageContent(language);
  }

  // Get services features content with fallback
  static async getServicesFeaturesContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await this.pb.getContentSection('home_services', language);

      if (content && content.enabled) {
        const data = content.data || {};

        return {
          enable: content.enabled,
          subtitle: content.subtitle || (language === 'fr' ? "Pourquoi Choisir PTBL" : "Why Choose PTBL"),
          title: content.title || (language === 'fr' ? "Nos Avantages de Service" : "Our Service Advantages"),
          feature_item: data.feature_item || []
        };
      }
    } catch (_error) {
      console.warn('Failed to fetch services features content from PocketBase, using fallback');
    }

    return this.getFallbackServicesFeaturesContent(language);
  }

  // Get team page content with fallback
  static async getTeamContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await this.pb.getContentSection('team_page', language);

      if (content && content.enabled) {
        const data = content.data || {};

        return {
          enable: content.enabled,
          subtitle: content.subtitle || (language === 'fr' ? "Équipe de Direction" : "Management Team"),
          title: content.title || (language === 'fr' ? "Rencontrez Notre Leadership" : "Meet Our Leadership"),
          team_member: data.team_member || []
        };
      }
    } catch (_error) {
      console.warn('Failed to fetch team content from PocketBase, using fallback');
    }

    return this.getFallbackTeamContent(language);
  }

  // Get FAQ content with fallback
  static async getFaqContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await this.pb.getContentSection('faq_page', language);

      if (content && content.enabled) {
        const data = content.data || {};

        return {
          enable: content.enabled,
          subtitle: content.subtitle || "FAQ",
          title: content.title || (language === 'fr' ? "Questions Fréquemment Posées" : "Frequently Asked Questions"),
          faq_item: data.faq_item || []
        };
      }
    } catch (_error) {
      console.warn('Failed to fetch FAQ content from PocketBase, using fallback');
    }

    return this.getFallbackFaqContent(language);
  }

  // Get contact page content with fallback
  static async getContactPageContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await this.pb.getContentSection('contact_page', language);

      if (content && content.enabled) {
        const data = content.data || {};

        return {
          enable: content.enabled,
          title: content.title || (language === 'fr' ? "Contactez-nous" : "Contact Us"),
          subtitle: content.subtitle || (language === 'fr' ? "Prêt à améliorer votre connectivité? Discutons de vos besoins en réseau de fibres." : "Ready to enhance your connectivity? Let's discuss your fiber network requirements."),
          description: content.content || (language === 'fr' ? "Contactez PTBL pour des solutions fiables de réseau de fibres optiques, des services d'infrastructure et des conseils techniques. Nous fournissons des solutions de connectivité complètes à travers le Ghana." : "Get in touch with PTBL for reliable fiber optic network solutions, infrastructure services, and technical consultancy. We provide comprehensive connectivity solutions across ECG's operational area."),
          contact_info: data.contact_info || {
            address: language === 'fr' ? "2ème étage, bâtiment Omanye Aba, en face du stade Ohene Djan Sports" : "2nd Floor, Omanye Aba building, opposite Ohene Djan Sports Stadium",
            phone: "+233 24-388-9991",
            email: "<EMAIL>",
            hours: language === 'fr' ? "Lundi - Vendredi: 8h00 - 17h00" : "Monday - Friday: 8:00 AM - 5:00 PM"
          },
          form_labels: data.form_labels || {
            name: language === 'fr' ? "Nom Complet" : "Full Name",
            email: language === 'fr' ? "Adresse Email" : "Email Address",
            phone: language === 'fr' ? "Numéro de Téléphone" : "Phone Number",
            subject: language === 'fr' ? "Sujet" : "Subject",
            message: language === 'fr' ? "Message" : "Message",
            submit: language === 'fr' ? "Envoyer le Message" : "Send Message"
          }
        };
      }
    } catch (_error) {
      console.warn('Failed to fetch contact page content from PocketBase, using fallback');
    }

    return this.getFallbackContactPageContent(language);
  }

  // Get image URL with fallback
  static async getImageUrl(imageKey: string, fallbackUrl: string, thumb?: string): Promise<string> {
    try {
      const image = await this.pb.getImage(imageKey);
      if (image) {
        return this.pb.getImageUrl(image, thumb);
      }
    } catch (_error) {
      console.warn(`Failed to fetch image ${imageKey} from PocketBase, using fallback`);
    }
    
    return fallbackUrl;
  }

  // Generic content fetcher
  static async getContent(sectionKey: string, language: 'en' | 'fr' = 'en', fallback: Record<string, unknown> = {}) {
    try {
      const content = await this.pb.getContentSection(sectionKey, language);
      
      if (content && content.enabled) {
        return {
          ...fallback,
          ...content.data,
          enable: content.enabled,
          title: content.title,
          subtitle: content.subtitle,
          content: content.content
        };
      }
    } catch (_error) {
      console.warn(`Failed to fetch content ${sectionKey} from PocketBase, using fallback`);
    }
    
    return fallback;
  }

  // Fallback content methods
  private static getFallbackBannerContent(language: 'en' | 'fr') {
    return language === 'fr' ? {
      enable: true,
      bg_image: "images/background/hero.webp",
      title: "Nous Connectons. Vous Alimentez. Le Monde Fonctionne",
      subtitle: "Excellence du Réseau de Fibres",
      watermark: "PTBL",
      content: "Autonomiser les entreprises et les communautés avec une infrastructure de réseau de fibres de pointe.",
      features: [
        "99,9% de Disponibilité du Réseau",
        "Support Expert 24/7",
        "Dans la zone opérationnelle d'ECG"
      ],
      button: {
        enable: true,
        label: "Contactez-nous",
        link: "/fr/contact"
      },
      secondary_button: {
        enable: true,
        label: "Voir les Services",
        link: "/fr/services"
      }
    } : {
      enable: true,
      bg_image: "images/background/hero.webp",
      title: "We Connect. You Power. The World Runs",
      subtitle: "Fiber Network Excellence",
      watermark: "PTBL",
      content: "Empowering businesses and communities with cutting-edge fiber network infrastructure.",
      features: [
        "99.9% Network Uptime",
        "24/7 Expert Support",
        "Across ECG's operational area"
      ],
      button: {
        enable: true,
        label: "Contact Us",
        link: "/contact"
      },
      secondary_button: {
        enable: true,
        label: "View Services",
        link: "/services"
      }
    };
  }

  private static getFallbackAboutContent(language: 'en' | 'fr') {
    return language === 'fr' ? {
      enable: true,
      about_item: [{
        image: "/images/about/framework.png",
        title: "HISTOIRE DE L'ENTREPRISE",
        content: "Power Telco Business Limited (PTBL) est une filiale entièrement détenue d'ECG...",
        button: {
          enable: true,
          label: "En Savoir Plus",
          link: "/fr/about"
        }
      }]
    } : {
      enable: true,
      about_item: [{
        image: "/images/about/framework.png",
        title: "COMPANY HISTORY",
        content: "Power Telco Business Limited (PTBL) is ECG's wholly owned subsidiary established to leverage the company's extensive fiber optic infrastructure. Built on ECG's power transmission network, PTBL provides comprehensive telecommunications services across Ghana, connecting businesses and communities with reliable, high-speed fiber connectivity.",
        button: {
          enable: true,
          label: "Learn More",
          link: "/about"
        }
      }]
    };
  }

  private static getFallbackFeaturesContent(language: 'en' | 'fr') {
    const defaultFeatures = language === 'fr' ? [
      {
        name: "Services Réseau ECG",
        image: "/images/feature/feature-1.jpeg",
        content: "Services complets de réseau de fibres incluant les Services de Ligne Louée, la Planification de Réseau, et les Services d'Optimisation de Réseau pour les opérations d'ECG.",
        icon: "fas fa-network-wired",
        color: "blue"
      },
      {
        name: "Services Réseau Commerciaux",
        image: "/images/feature/feature-2.jpeg",
        content: "En collaboration avec notre partenaire commercial, nous offrons des services professionnels de réseau de fibres incluant les Services de Location de Fibres, le Service de Colocation POP, et le Service de Location de Poteaux pour les entreprises.",
        icon: "fas fa-building",
        color: "green"
      },
      {
        name: "Services de Conseil Technique",
        image: "/images/feature/feature-3.jpeg",
        content: "Services experts de support technique et de conseil pour optimiser votre infrastructure et opérations réseau.",
        icon: "fas fa-broadcast-tower",
        color: "purple"
      }
    ] : [
      {
        name: "ECG Network Services",
        image: "/images/feature/feature-1.jpeg",
        content: "Comprehensive fiber network services including Leased Line Services, Network Planning, and Network Optimization Services for ECG operations.",
        icon: "fas fa-network-wired",
        color: "blue"
      },
      {
        name: "Commercial Network Services",
        image: "/images/feature/feature-2.jpeg",
        content: "In collaboration with our commercial partner, we offer professional fiber network services including Fiber Lease Services, POP Colocation Service, and Pole Rental Service for businesses.",
        icon: "fas fa-building",
        color: "green"
      },
      {
        name: "Technical Consultancy Services",
        image: "/images/feature/feature-3.jpeg",
        content: "Expert technical support and consultancy services to optimize your network infrastructure and operations.",
        icon: "fas fa-broadcast-tower",
        color: "purple"
      }
    ];

    return {
      enable: true,
      subtitle: language === 'fr' ? "NOS SERVICES" : "OUR SERVICES",
      title: language === 'fr' ? "Solutions Réseau Complètes" : "Comprehensive Network Solutions",
      content: language === 'fr' ? "alimentées par l'infrastructure de fibres d'ECG" : "powered by ECG's fiber infrastructure",
      feature_item: defaultFeatures
    };
  }

  private static getFallbackStatsContent(language: 'en' | 'fr') {
    const defaultStats = language === 'fr' ? [
      {
        icon: "fas fa-network-wired",
        number: "500+",
        label: "Kilomètres de Fibre"
      },
      {
        icon: "fas fa-building",
        number: "100+",
        label: "Entreprises Connectées"
      },
      {
        icon: "fas fa-clock",
        number: "99.9%",
        label: "Disponibilité"
      }
    ] : [
      {
        icon: "fas fa-network-wired",
        number: "500+",
        label: "Kilometers of Fiber"
      },
      {
        icon: "fas fa-building",
        number: "100+",
        label: "Connected Businesses"
      },
      {
        icon: "fas fa-clock",
        number: "99.9%",
        label: "Uptime"
      }
    ];

    return {
      enable: true,
      stats_item: defaultStats
    };
  }

  private static getFallbackPromoVideoContent(language: 'en' | 'fr') {
    return language === 'fr' ? {
      enable: true,
      video_thumbnail: "images/fibre_coverage.jpeg",
      video_link: "",
      subtitle: "Couverture Réseau",
      title: "Étendre la Portée Numérique du Ghana",
      content: "Notre technologie de Câble de Garde Optique (OPGW) offre une fiabilité et des performances inégalées à travers <a href='https://www.ecg.com.gh' target='_blank' class='text-primary hover:underline font-semibold'>la zone opérationnelle d'Electricity Company of Ghana</a>.<br><br>Avec une couverture stratégique s'étendant des centres urbains aux communautés rurales, nous fournissons des solutions de connectivité sécurisées et haute vitesse qui permettent aux entreprises de prospérer à l'ère numérique.",
      features: [
        "Réseau OPGW National",
        "Infrastructure Redondante",
        "Surveillance 24/7",
        "Sécurité de Niveau Entreprise"
      ]
    } : {
      enable: true,
      video_thumbnail: "images/fibre_coverage.jpeg",
      video_link: "",
      subtitle: "Network Coverage",
      title: "Expanding Ghana's Digital Reach",
      content: "Our Optical Ground Wire (OPGW) technology delivers unmatched reliability and performance across <a href='https://www.ecg.com.gh' target='_blank' class='text-primary hover:underline font-semibold'>Electricity Company of Ghana's</a> operational area.<br><br>With strategic coverage spanning urban centers to rural communities, we provide secure, high-speed connectivity solutions that enable businesses to thrive in the digital age.",
      features: [
        "Nationwide OPGW Network",
        "Redundant Infrastructure",
        "24/7 Monitoring",
        "Enterprise-Grade Security"
      ]
    };
  }

  private static getFallbackShowcaseContent(language: 'en' | 'fr') {
    return language === 'fr' ? {
      enable: true,
      subtitle: "Pourquoi Choisir PTBL",
      title: "Permettre la Transformation Numérique du Ghana",
      content: "Découvrez notre vaste infrastructure de réseau de fibres optiques qui s'étend à travers la zone opérationnelle d'ECG. Avec une couverture stratégique et une technologie OPGW de pointe, nous livrons des solutions de connectivité fiables pour les fournisseurs de télécommunications et les entreprises.",
      highlights: [
        {
          icon: "fas fa-shield-alt",
          title: "Sécurisé & Fiable",
          description: "Sécurité de niveau militaire avec garantie de disponibilité de 99,9%"
        },
        {
          icon: "fas fa-rocket",
          title: "Haute Performance",
          description: "Latence ultra-faible et transmission de données haute vitesse"
        },
        {
          icon: "fas fa-users",
          title: "Support Expert",
          description: "Support technique 24/7 de professionnels certifiés"
        },
        {
          icon: "fas fa-expand-arrows-alt",
          title: "Solutions Évolutives",
          description: "Infrastructure flexible qui grandit avec votre entreprise"
        }
      ]
    } : {
      enable: true,
      subtitle: "Why Choose PTBL",
      title: "Enabling Ghana's Digital Transformation",
      content: "Discover our extensive fiber optic network infrastructure that spans across ECG's operational area. With strategic coverage and state-of-the-art OPGW technology, we deliver reliable connectivity solutions for telecommunications providers and enterprises.",
      highlights: [
        {
          icon: "fas fa-shield-alt",
          title: "Secure & Reliable",
          description: "Military-grade security with 99.9% uptime guarantee"
        },
        {
          icon: "fas fa-rocket",
          title: "High Performance",
          description: "Ultra-low latency and high-speed data transmission"
        },
        {
          icon: "fas fa-users",
          title: "Expert Support",
          description: "24/7 technical support from certified professionals"
        },
        {
          icon: "fas fa-expand-arrows-alt",
          title: "Scalable Solutions",
          description: "Flexible infrastructure that grows with your business"
        }
      ]
    };
  }

  private static getFallbackCtaContent(language: 'en' | 'fr') {
    return language === 'fr' ? {
      enable: true,
      bg_image: "images/background/cta-bg.jpg",
      subtitle: "Prêt à Se Connecter?",
      title: "Construisons Votre Solution Réseau",
      content: "Rejoignez un nombre croissant d'entreprises déjà alimentées par l'infrastructure de réseau de fibres de PTBL. Commencez avec une consultation gratuite aujourd'hui.",
      button: {
        enable: true,
        label: "Consultation Gratuite",
        link: "/fr/contact"
      },
      secondary_button: {
        enable: true,
        label: "Appeler +233 24-388-9991",
        link: "tel:+233243889991"
      }
    } : {
      enable: true,
      bg_image: "images/background/cta-bg.jpg",
      subtitle: "Ready to Connect?",
      title: "Let's Build Your Network Solution",
      content: "Join growing number of businesses already powered by PTBL's fiber network infrastructure. Get started with a free consultation today.",
      button: {
        enable: true,
        label: "Get Free Consultation",
        link: "/contact"
      },
      secondary_button: {
        enable: true,
        label: "Call +233 24-388-9991",
        link: "tel:+233243889991"
      }
    };
  }

  private static getFallbackFooterContent(language: 'en' | 'fr') {
    return language === 'fr' ? {
      enable: true,
      company: 'Entreprise',
      quickLinks: 'Liens Rapides',
      followUs: 'Suivez-nous',
      contactInfo: 'Informations de Contact',
      description: 'Nous visons à tirer parti de la bonne volonté d\'ECG, des actifs de fibres optiques et des capacités techniques pour devenir le fournisseur leader de solutions innovantes et durables pour ECG et d\'autres entités dans la sous-région.',
      address: '2ème étage, bâtiment Omanye Aba, en face du stade Ohene Djan Sports',
      phone: '+233 24-388-9991',
      email: '<EMAIL>',
      copyright: '© 2024 Power Telco Business Limited. Tous droits réservés.',
      poweredBy: 'Propulsé par',
      links: [
        { name: 'À propos', url: '/fr/about' },
        { name: 'Services', url: '/fr/services' },
        { name: 'Équipe', url: '/fr/team' },
        { name: 'Contact', url: '/fr/contact' },
        { name: 'Politique de confidentialité', url: '/fr/privacy' },
        { name: 'Conditions de service', url: '/fr/terms' }
      ],
      socialLinks: [
        { name: 'LinkedIn', url: 'https://www.linkedin.com/company/power-telco-business-limted/', icon: 'fab fa-linkedin-in' }
      ]
    } : {
      enable: true,
      company: 'Company',
      quickLinks: 'Quick Links',
      followUs: 'Follow Us',
      contactInfo: 'Contact Info',
      description: 'We aim to leverage ECG\'s goodwill, fiber optic assets and technical capacities to become the leading provider of innovative and sustainable solutions to ECG and other entities in the subregion.',
      address: '2nd Floor, Omanye Aba building, opposite Ohene Djan Sports Stadium',
      phone: '+233 24-388-9991',
      email: '<EMAIL>',
      copyright: '© 2024 Power Telco Business Limited. All rights reserved.',
      poweredBy: 'Powered by',
      links: [
        { name: 'About Us', url: '/about' },
        { name: 'Services', url: '/services' },
        { name: 'Team', url: '/team' },
        { name: 'Contact', url: '/contact' },
        { name: 'Privacy Policy', url: '/privacy' },
        { name: 'Terms of Service', url: '/terms' }
      ],
      socialLinks: [
        { name: 'LinkedIn', url: 'https://www.linkedin.com/company/power-telco-business-limted/', icon: 'fab fa-linkedin-in' }
      ]
    };
  }

  private static getFallbackAboutPageContent(language: 'en' | 'fr') {
    return language === 'fr' ? {
      enable: true,
      video_thumbnail: "/images/about/framework.png",
      title: "HISTOIRE DE L'ENTREPRISE",
      content: "Constituée le 18 janvier 2023, Power Telco Business Limited (PTBL) a commencé ses opérations en juin 2024 en tant que filiale entièrement détenue de <a href='https://www.ecg.com.gh' target='_blank' class='text-primary hover:underline'>la Compagnie d'Électricité du Ghana</a>, avec pour mandat de commercialiser ses actifs de fibres optiques et autres.<br><br>PTBL exploite l'infrastructure étendue de fibres optiques d'ECG, construite sur la technologie de Fil de Terre Optique (OPGW), pour fournir des services réseau complets. Notre position stratégique au sein du cadre opérationnel d'ECG nous permet de livrer des solutions de connectivité fiables et haute performance à travers le Ghana.<br><br>En tant que filiale d'ECG, nous combinons des décennies d'expertise en services publics avec une technologie de télécommunications de pointe pour servir à la fois les besoins internes d'ECG et les clients commerciaux externes, stimulant la croissance durable et l'innovation dans le paysage de l'infrastructure numérique du Ghana.",
      button: {
        enable: true,
        label: "Contactez-nous",
        link: "/fr/contact"
      }
    } : {
      enable: true,
      video_thumbnail: "/images/about/framework.png",
      title: "COMPANY HISTORY",
      content: "Incorporated on January 18th, 2023, Power Telco Business Limited (PTBL) began operations in June 2024 as <a href='https://www.ecg.com.gh' target='_blank' class='text-primary hover:underline'>Electricity Company of Ghana's</a> wholly owned subsidiary, with a mandate to commercialize its fiber optic and other assets.<br><br>PTBL leverages ECG's extensive fiber optic infrastructure, built on Optical Ground Wire (OPGW) technology, to provide comprehensive network services. Our strategic position within ECG's operational framework enables us to deliver reliable, high-performance connectivity solutions across ECG's operational area.<br><br>As a subsidiary of ECG, we combine decades of utility expertise with cutting-edge telecommunications technology to serve both ECG's internal needs and external commercial clients, driving sustainable growth and innovation in Ghana's digital infrastructure landscape.",
      button: {
        enable: true,
        label: "Contact Us",
        link: "/contact"
      }
    };
  }

  private static getFallbackVisionMissionContent(language: 'en' | 'fr') {
    return language === 'fr' ? {
      enable: true,
      items: [
        {
          title: "Notre Vision",
          content: "Exploiter la bonne volonté d'ECG, les actifs de fibres optiques et les capacités techniques pour devenir le fournisseur leader de solutions innovantes et durables pour ECG et d'autres entités dans la sous-région."
        },
        {
          title: "Notre Mission",
          content: "Notre mission est de stimuler la croissance durable pour ECG en générant une partie majeure des revenus non tarifaires d'ECG."
        }
      ]
    } : {
      enable: true,
      items: [
        {
          title: "Our Vision",
          content: "To leverage ECG's goodwill, fiber optic assets and technical capacities to become the leading provider of innovative and sustainable solutions to ECG and other entities in the subregion."
        },
        {
          title: "Our Mission",
          content: "Our mission is to drive sustainable growth for ECG by generating a major portion of ECG's non-tariff revenue."
        }
      ]
    };
  }

  private static getFallbackServicesPageContent(language: 'en' | 'fr') {
    return language === 'fr' ? {
      enable: true,
      subtitle: "Nos Services",
      title: "Solutions Réseau Complètes",
      service_item: [
        {
          title: "Services de Ligne Louée",
          icon: "fas fa-network-wired",
          icon_color: "blue",
          content: "Solutions de connectivité point à point dédiées offrant une bande passante garantie et des performances fiables pour les opérations commerciales critiques."
        },
        {
          title: "Planification Réseau",
          icon: "fas fa-project-diagram",
          icon_color: "green",
          content: "Services de conception et de planification réseau stratégiques pour optimiser votre infrastructure pour les besoins actuels et la croissance future."
        },
        {
          title: "Optimisation Réseau",
          icon: "fas fa-chart-line",
          icon_color: "purple",
          content: "Services d'amélioration des performances pour maximiser l'efficacité du réseau, réduire la latence et améliorer la fiabilité globale du système."
        },
        {
          title: "Services de Location de Fibres",
          icon: "fas fa-ethernet",
          icon_color: "orange",
          content: "Options de location d'infrastructure de fibres optiques flexibles pour les fournisseurs de télécommunications et les entreprises nécessitant une connectivité haute capacité."
        },
        {
          title: "Service de Colocation POP",
          icon: "fas fa-server",
          icon_color: "red",
          content: "Services de colocation Point de Présence offrant des installations sécurisées et climatisées pour votre équipement réseau et infrastructure."
        },
        {
          title: "Service de Location de Poteaux",
          icon: "fas fa-broadcast-tower",
          icon_color: "indigo",
          content: "Services de location de poteaux utilitaires exploitant l'infrastructure étendue d'ECG pour soutenir le déploiement d'équipements de télécommunications."
        }
      ]
    } : {
      enable: true,
      subtitle: "Our Services",
      title: "Comprehensive Network Solutions",
      service_item: [
        {
          title: "Leased Line Services",
          icon: "fas fa-network-wired",
          icon_color: "blue",
          content: "Dedicated point-to-point connectivity solutions providing guaranteed bandwidth and reliable performance for critical business operations."
        },
        {
          title: "Network Planning",
          icon: "fas fa-project-diagram",
          icon_color: "green",
          content: "Strategic network design and planning services to optimize your infrastructure for current needs and future growth."
        },
        {
          title: "Network Optimization",
          icon: "fas fa-chart-line",
          icon_color: "purple",
          content: "Performance enhancement services to maximize network efficiency, reduce latency, and improve overall system reliability."
        },
        {
          title: "Fiber Lease Services",
          icon: "fas fa-ethernet",
          icon_color: "orange",
          content: "Flexible fiber optic infrastructure leasing options for telecommunications providers and enterprises requiring high-capacity connectivity."
        },
        {
          title: "POP Colocation Service",
          icon: "fas fa-server",
          icon_color: "red",
          content: "Point of Presence colocation services providing secure, climate-controlled facilities for your network equipment and infrastructure."
        },
        {
          title: "Pole Rental Service",
          icon: "fas fa-broadcast-tower",
          icon_color: "indigo",
          content: "Utility pole rental services leveraging ECG's extensive infrastructure to support telecommunications equipment deployment."
        }
      ]
    };
  }

  private static getFallbackServicesFeaturesContent(language: 'en' | 'fr') {
    return language === 'fr' ? {
      enable: true,
      subtitle: "Pourquoi Choisir PTBL",
      title: "Nos Avantages de Service",
      feature_item: [
        {
          name: "Technologie OPGW",
          image: "/images/feature/feature-1.jpeg",
          content: "Technologie de pointe de Fil de Terre Optique intégrée à l'infrastructure électrique d'ECG pour une fiabilité et une couverture maximales."
        },
        {
          name: "Support 24/7",
          image: "/images/feature/feature-2.jpeg",
          content: "Support technique et surveillance 24h/24 pour garantir que votre réseau fonctionne à des performances optimales avec un temps d'arrêt minimal."
        },
        {
          name: "Solutions Évolutives",
          image: "/images/feature/feature-3.jpeg",
          content: "Solutions réseau flexibles et évolutives qui grandissent avec les besoins de votre entreprise, des petites entreprises aux grands fournisseurs de télécommunications."
        }
      ]
    } : {
      enable: true,
      subtitle: "Why Choose PTBL",
      title: "Our Service Advantages",
      feature_item: [
        {
          name: "OPGW Technology",
          image: "/images/feature/feature-1.jpeg",
          content: "State-of-the-art Optical Ground Wire technology integrated with ECG's power infrastructure for maximum reliability and coverage."
        },
        {
          name: "24/7 Support",
          image: "/images/feature/feature-2.jpeg",
          content: "Round-the-clock technical support and monitoring to ensure your network operates at peak performance with minimal downtime."
        },
        {
          name: "Scalable Solutions",
          image: "/images/feature/feature-3.jpeg",
          content: "Flexible and scalable network solutions that grow with your business needs, from small enterprises to large telecommunications providers."
        }
      ]
    };
  }

  private static getFallbackTeamContent(language: 'en' | 'fr') {
    return language === 'fr' ? {
      enable: true,
      subtitle: "Équipe de Direction",
      title: "Rencontrez Notre Leadership",
      team_member: [
        {
          name: "Leonardo Lamptey",
          image: "/images/team/leonardo.jpeg",
          designation: "Directeur Général",
          bio: "Notre PDG est avocat de profession avec plus de 30 ans de service public national et international, de pratique privée et d'expertise dans les domaines de la promotion des investissements, de l'arbitrage international, de la négociation de coentreprises, d'accords de transfert de technologie et de la fourniture d'autres services de conseil aux entreprises. Il détient un diplôme de troisième cycle en droit international de l'Institut d'études sociales de La Haye, un certificat professionnel en droit/certificat d'avocat (B.L.) de l'École de droit du Ghana et un diplôme de licence en droit (LL.B Hons) de l'Université du Ghana, Legon. Il a travaillé comme secrétaire avocat du Centre de promotion des investissements du Ghana (GIPC), consultant/expert en promotion des investissements pour l'Organisation des Nations Unies pour le développement industriel (ONUDI), secrétaire du conseil d'administration et directeur des services juridiques de la Compagnie d'électricité du Ghana (ECG) et conseiller en arbitrage international pour ECG. Maître Lamptey a joué un rôle central dans la modélisation et la négociation du modèle commercial pour la création de PTBL et de sa filiale de coentreprise P-Tel Limited et a donc été chargé de diriger le processus de mise en place opérationnelle et de commercialisation de PTBL.",
          social: [
            {
              icon: "fab fa-linkedin",
              link: "#"
            }
          ]
        },
        {
          name: "George Eduful CEng MIET (UK), FPE (IET-Gh), SMIEEE, PhD",
          image: "/images/team/george.jpeg",
          designation: "Directeur des Opérations",
          bio: "Dr. George Eduful est un ingénieur électricien accompli et un professionnel distingué avec de nombreuses affiliations et réalisations. Il est enregistré comme ingénieur agréé auprès du Conseil d'ingénierie du Royaume-Uni et détient le titre d'ingénieur professionnel auprès du Conseil d'ingénierie du Ghana. De plus, il possède une bourse et une adhésion senior à l'Institution of Engineering and Technology (IET), Ghana et à l'Institute of Electrical and Electronics Engineers (IEEE), respectivement. Eduful a obtenu son doctorat en génie électrique de l'Université des mines et de la technologie. Dr. Eduful a été membre du panel d'experts CIGRE-Afrique de l'Ouest (Comité d'étude) et a été le coordinateur de conférence pour la Conférence et exposition internationale des services publics IEEE-IETGh en 2022. Il a également présidé la section IEEE Ghana à deux reprises. Ses honneurs incluent le prix de la meilleure présentation à la 18e Conférence internationale sur l'ingénierie des systèmes électriques et informatiques à Londres en 2016, un certificat de mérite en présentation de papier à la Conférence internationale 2016 des ingénieurs électriques et électroniques à Londres, et le prix du meilleur employé de la Direction d'ingénierie de la Compagnie d'électricité du Ghana en 2012. Dr. Eduful est un auteur prolifique, ayant publié plus de 100 articles dans des revues internationales réputées. Actuellement, il sert comme directeur des opérations de Power Telco Business Limited (PTBL), une filiale de la Compagnie d'électricité du Ghana. Dans ce rôle, il fournit des conseils stratégiques et travaille en étroite collaboration avec un partenaire de coentreprise pour stimuler la croissance durable d'ECG en générant des revenus non tarifaires grâce aux services liés à la fibre et à d'autres solutions innovantes. De plus, il travaille comme consultant, spécialisé dans les études de qualité de l'énergie et les audits énergétiques pour les institutions corporatives et étatiques, y compris GCB Bank, la Cour suprême, le Parlement du Ghana et diverses industries minières.",
          social: [
            {
              icon: "fab fa-linkedin",
              link: "#"
            }
          ]
        }
      ]
    } : {
      enable: true,
      subtitle: "Management Team",
      title: "Meet Our Leadership",
      team_member: [
        {
          name: "Leonardo Lamptey",
          image: "/images/team/leonardo.jpeg",
          designation: "Chief Executive Officer",
          bio: "Our CEO is a lawyer by profession with over 30 years of national and international public service, private practice and expertise in the areas of investment promotion, international arbitration, negotiation of joint ventures, technology transfer agreements and provision of other business advisory services. He holds a Post Graduate Diploma in International Law from the Institute of Social Studies, The Hague, Professional Certificate in Law/Barrister-at-Law Certificate (B.L.) from the Ghana School of Law and a Bachelor of Laws degree (LL.B Hons) from the University of Ghana, Legon. He worked as the Solicitor Secretary of the Ghana Investment Promotion Centre (GIPC), Consultant/Investment Promotion Expert for the United Nations Industrial Organization (UNIDO), Secretary to the Board and Director for Legal Services of the Electricity Company of Ghana (ECG) and International Arbitration Advisor for ECG. Lawyer Lamptey played a pivotal role in modeling and negotiating the business model for setting up of PTBL and its joint venture subsidiary P-Tel Limited and has therefore been charged to lead the operational set up and commercialization process of PTBL.",
          social: [
            {
              icon: "fab fa-linkedin",
              link: "#"
            }
          ]
        },
        {
          name: "George Eduful CEng MIET (UK), FPE (IET-Gh), SMIEEE, PhD",
          image: "/images/team/george.jpeg",
          designation: "Chief Operating Officer",
          bio: "Dr. George Eduful is an accomplished electrical engineer and a distinguished professional with numerous affiliations and achievements. He is registered as a Chartered Engineer with the Engineering Council of the UK and holds the title of Professional Engineer with the Engineering Council of Ghana. Additionally, he possesses fellowship and senior membership with the Institution of Engineering and Technology (IET), Ghana and the Institute of Electrical and Electronics Engineers (IEEE), respectively. Eduful earned his Ph.D. in Electrical Engineering from the University of Mines and Technology. Dr. Eduful has been a member of the CIGRE-West Africa Panel of Experts (Study Committee) and was the Conference Convener for the IEEE-IETGh International Utility Conference and Exposition in 2022. He has also chaired the IEEE Ghana Section on two separate occasions. His honors include the Best Presentation Award at the 18th International Conference on Electrical and Computer Systems Engineering in London in 2016, a Certificate of Merit in Paper Presentation at the 2016 International Conference of Electrical and Electronic Engineers in London, and the Best Employee Award from the Engineering Directorate of the Electricity Company of Ghana in 2012. Dr. Eduful is a prolific author, having published over 100 papers in reputable international journals. Currently, he serves as the Chief Operating Officer of Power Telco Business Limited (PTBL), a subsidiary of the Electricity Company of Ghana. In this role, he provides strategic guidance and working closely with a joint venture partner to drive sustainable growth for ECG by generating non-tariff revenue through fiber-related services and other innovative solutions. Additionally, he works as a consultant, specializing in power quality studies and energy audits for corporate and state institutions including GCB Bank, the Supreme Court, Parliament of Ghana, and various mining industries.",
          social: [
            {
              icon: "fab fa-linkedin",
              link: "#"
            }
          ]
        }
      ]
    };
  }

  private static getFallbackFaqContent(language: 'en' | 'fr') {
    return language === 'fr' ? {
      enable: true,
      subtitle: "FAQ",
      title: "Questions Fréquemment Posées",
      faq_item: [
        {
          question: "Qu'est-ce que PTBL et quels services offrez-vous ?",
          answer: "Power Telco Business Limited (PTBL) est la filiale entièrement détenue d'ECG spécialisée dans les services de réseau de fibres optiques. Nous offrons des Services de Ligne Louée, la Planification Réseau, l'Optimisation Réseau, les Services de Location de Fibres, la Colocation POP et les services de Location de Poteaux."
        },
        {
          question: "Qu'est-ce que la technologie OPGW et comment bénéficie-t-elle aux clients ?",
          answer: "Le Fil de Terre Optique (OPGW) est un câble à double fonction qui sert à la fois de fil de terre protecteur pour les systèmes électriques et de support de communication par fibres optiques. Cette technologie offre une fiabilité exceptionnelle, une couverture étendue grâce à l'infrastructure électrique d'ECG, et un déploiement rentable."
        },
        {
          question: "Quelles zones la couverture réseau de PTBL inclut-elle ?",
          answer: "Notre couverture réseau s'étend à travers la zone opérationnelle d'ECG dans tout le Ghana. Nous exploitons l'infrastructure électrique étendue d'ECG pour fournir une connectivité fibre complète aux zones urbaines et rurales à travers le pays."
        },
        {
          question: "Quelle est la fiabilité des services réseau de PTBL ?",
          answer: "Nous offrons des garanties de disponibilité de pointe allant de 99,5% à 99,99% selon votre plan de service. Notre réseau est construit sur l'infrastructure électrique robuste d'ECG avec des chemins redondants et une surveillance 24/7."
        },
        {
          question: "Quelles options de support sont disponibles ?",
          answer: "Nous fournissons plusieurs niveaux de support incluant le support par email, le support téléphonique prioritaire, et des équipes de support dédiées 24/7 selon votre plan de service. Les clients entreprise reçoivent également un support technique sur site."
        }
      ]
    } : {
      enable: true,
      subtitle: "FAQ",
      title: "Frequently Asked Questions",
      faq_item: [
        {
          question: "What is PTBL and what services do you offer?",
          answer: "Power Telco Business Limited (PTBL) is ECG's wholly owned subsidiary specializing in fiber optic network services. We offer Leased Line Services, Network Planning, Network Optimization, Fiber Lease Services, POP Colocation, and Pole Rental services."
        },
        {
          question: "What is OPGW technology and how does it benefit customers?",
          answer: "Optical Ground Wire (OPGW) is a dual-function cable that serves as both a protective ground wire for electrical systems and a fiber optic communication medium. This technology provides exceptional reliability, extensive coverage through ECG's power infrastructure, and cost-effective deployment."
        },
        {
          question: "What areas does PTBL's network coverage include?",
          answer: "Our network coverage spans across ECG's operational area throughout Ghana. We leverage ECG's extensive power infrastructure to provide comprehensive fiber connectivity to urban and rural areas across the country."
        },
        {
          question: "How reliable are PTBL's network services?",
          answer: "We offer industry-leading uptime guarantees ranging from 99.5% to 99.99% depending on your service plan. Our network is built on ECG's robust power infrastructure with redundant pathways and 24/7 monitoring."
        },
        {
          question: "What support options are available?",
          answer: "We provide multiple support tiers including email support, priority phone support, and 24/7 dedicated support teams depending on your service plan. Enterprise customers also receive on-site technical support."
        }
      ]
    };
  }

  private static getFallbackContactPageContent(language: 'en' | 'fr') {
    return language === 'fr' ? {
      enable: true,
      title: "Contactez-nous",
      subtitle: "Prêt à améliorer votre connectivité? Discutons de vos besoins en réseau de fibres.",
      description: "Contactez PTBL pour des solutions fiables de réseau de fibres optiques, des services d'infrastructure et des conseils techniques. Nous fournissons des solutions de connectivité complètes à travers le Ghana.",
      contact_info: {
        address: "2ème étage, bâtiment Omanye Aba, en face du stade Ohene Djan Sports",
        phone: "+233 24-388-9991",
        email: "<EMAIL>",
        hours: "Lundi - Vendredi: 8h00 - 17h00"
      },
      form_labels: {
        name: "Nom Complet",
        email: "Adresse Email",
        phone: "Numéro de Téléphone",
        subject: "Sujet",
        message: "Message",
        submit: "Envoyer le Message"
      }
    } : {
      enable: true,
      title: "Contact Us",
      subtitle: "Ready to enhance your connectivity? Let's discuss your fiber network requirements.",
      description: "Get in touch with PTBL for reliable fiber optic network solutions, infrastructure services, and technical consultancy. We provide comprehensive connectivity solutions across ECG's operational area.",
      contact_info: {
        address: "2nd Floor, Omanye Aba building, opposite Ohene Djan Sports Stadium",
        phone: "+233 24-388-9991",
        email: "<EMAIL>",
        hours: "Monday - Friday: 8:00 AM - 5:00 PM"
      },
      form_labels: {
        name: "Full Name",
        email: "Email Address",
        phone: "Phone Number",
        subject: "Subject",
        message: "Message",
        submit: "Send Message"
      }
    };
  }
}
