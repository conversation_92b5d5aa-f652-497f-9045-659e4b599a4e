# PTBL Admin Content Management System Setup Guide

This guide will help you set up the admin content management system for the PTBL website using PocketBase as the backend.

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ installed
- PocketBase binary downloaded
- Basic understanding of web development

### 1. Install Dependencies

```bash
npm install
```

### 2. Set up PocketBase

#### Download PocketBase
1. Visit [PocketBase releases](https://github.com/pocketbase/pocketbase/releases)
2. Download the appropriate binary for your system
3. Extract and place the `pocketbase` binary in your project root or system PATH

#### Start PocketBase
```bash
# In your project directory
./pocketbase serve
```

PocketBase will start on `http://127.0.0.1:8090` by default.

### 3. Configure Environment

Copy the example environment file:
```bash
cp .env.example .env
```

Update `.env` with your PocketBase URL:
```env
PUBLIC_POCKETBASE_URL=http://127.0.0.1:8090
```

### 4. Set up PocketBase Collections

The collections have been automatically created via the MCP server. You should see:

- `content_sections` - For managing website content
- `site_images` - For managing uploaded images  
- `admin_users` - For admin authentication

### 5. Create Admin User

#### Option A: Using PocketBase Admin UI
1. Visit `http://127.0.0.1:8090/_/`
2. Create an admin account for PocketBase itself
3. Navigate to the `admin_users` collection
4. Create a new record with:
   - **email**: <EMAIL>
   - **password**: your-secure-password
   - **name**: Your Name
   - **role**: admin
   - **active**: true

#### Option B: Using the MCP Server (if available)
The admin user creation can be done programmatically via the PocketBase MCP server.

### 6. Start the Development Server

```bash
npm run dev
```

### 7. Access the Admin Interface

1. Visit `http://localhost:4321/admin/login`
2. Log in with your admin credentials
3. Access the content management at `http://localhost:4321/edit-ptblgh`

## 📋 Collection Schemas

### content_sections
Stores editable content sections for the website.

| Field | Type | Description |
|-------|------|-------------|
| section_key | text | Unique identifier (e.g., "home_banner") |
| language | select | Language code ("en" or "fr") |
| title | text | Section title |
| subtitle | text | Section subtitle |
| content | editor | Rich text content |
| data | json | Additional structured data |
| enabled | bool | Whether section is active |
| sort_order | number | Display order |

### site_images
Manages uploaded images for the website.

| Field | Type | Description |
|-------|------|-------------|
| image_key | text | Unique identifier (e.g., "hero_background") |
| alt_text | text | Accessibility text |
| file | file | The uploaded image file |
| description | text | Image description |
| category | select | Image category (banner, about, team, etc.) |
| active | bool | Whether image is active |

### admin_users
Authentication collection for admin users.

| Field | Type | Description |
|-------|------|-------------|
| email | email | Login email (system field) |
| password | password | Encrypted password (system field) |
| name | text | Display name |
| role | select | User role (admin or editor) |
| active | bool | Account status |

## 🎯 Content Management

### Adding Content Sections

1. Go to the admin dashboard
2. Click "Content Sections" tab
3. Click "Add Section"
4. Fill in the form:
   - **Section Key**: Unique identifier (e.g., "home_banner")
   - **Language**: Choose English or French
   - **Title/Subtitle**: Display text
   - **Content**: Rich text content
   - **Additional Data**: JSON for complex data structures
   - **Enabled**: Toggle to show/hide

### Managing Images

1. Go to the admin dashboard
2. Click "Images" tab
3. Click "Upload Image"
4. Fill in the form:
   - **Image Key**: Unique identifier
   - **File**: Select image file (max 5MB)
   - **Alt Text**: Accessibility description
   - **Category**: Organize by type
   - **Active**: Toggle visibility

### Content Structure Examples

#### Banner Content
```json
{
  "bg_image": "images/background/hero.webp",
  "watermark": "PTBL",
  "features": [
    "99.9% Network Uptime",
    "24/7 Expert Support",
    "Across ECG's operational area"
  ],
  "button": {
    "enable": true,
    "label": "Contact Us",
    "link": "/contact"
  },
  "secondary_button": {
    "enable": true,
    "label": "View Services", 
    "link": "/services"
  }
}
```

#### Features Content
```json
{
  "feature_item": [
    {
      "icon": "fas fa-network-wired",
      "title": "Leased Line Services",
      "content": "High-speed dedicated connectivity for businesses."
    }
  ]
}
```

## 🔧 Development

### File Structure
```
src/
├── lib/
│   ├── pocketbase.ts          # PocketBase client utilities
│   ├── content.ts             # Client-side content fetching
│   └── server-content.ts      # Server-side content fetching
├── components/
│   └── admin/
│       └── Modal.ts           # Admin UI components
├── pages/
│   ├── edit-ptblgh.astro      # Main admin dashboard
│   └── admin/
│       └── login.astro        # Admin login page
└── middleware.ts              # Route protection
```

### Adding New Content Types

1. **Update the content fetcher**:
   ```typescript
   // In src/lib/server-content.ts
   static async getNewSectionContent(language: 'en' | 'fr' = 'en') {
     const content = await this.pb.getContentSection('new_section', language);
     // Handle content and fallback
   }
   ```

2. **Update the page**:
   ```astro
   ---
   const newSectionData = await ServerContentFetcher.getNewSectionContent('en');
   ---
   ```

3. **Add admin interface**:
   Add editing capabilities in the admin dashboard.

## 🚀 Deployment

### Production Setup

1. **Deploy PocketBase**:
   - Use a cloud provider (Railway, DigitalOcean, etc.)
   - Set up persistent storage for the database
   - Configure environment variables

2. **Update Environment**:
   ```env
   PUBLIC_POCKETBASE_URL=https://your-pocketbase-instance.com
   ```

3. **Deploy Astro Site**:
   ```bash
   npm run build
   ```

### Security Considerations

- Use strong passwords for admin accounts
- Enable HTTPS for production
- Regularly backup the PocketBase database
- Consider IP restrictions for admin access
- Keep PocketBase updated

## 🔍 Troubleshooting

### Common Issues

1. **PocketBase Connection Failed**
   - Check if PocketBase is running
   - Verify the URL in environment variables
   - Check firewall settings

2. **Admin Login Not Working**
   - Verify admin user exists in `admin_users` collection
   - Check email and password
   - Ensure user is marked as active

3. **Content Not Loading**
   - Check PocketBase collections exist
   - Verify content is marked as enabled
   - Check browser console for errors

4. **Images Not Displaying**
   - Verify image upload was successful
   - Check image is marked as active
   - Verify file permissions

### Debug Mode

Enable debug logging by checking browser console and PocketBase logs:

```bash
# Start PocketBase with debug logging
./pocketbase serve --debug
```

## 📚 Additional Resources

- [PocketBase Documentation](https://pocketbase.io/docs/)
- [Astro Documentation](https://docs.astro.build/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)

## 🔄 Initial Data Setup

### Sample Content Sections

You can create these initial content sections to get started:

1. **Homepage Banner (English)**
   - Section Key: `home_banner`
   - Language: `en`
   - Title: `We Connect. You Power. The World Runs`
   - Subtitle: `Fiber Network Excellence`
   - Content: `Empowering businesses and communities with cutting-edge fiber network infrastructure.`

2. **Homepage Banner (French)**
   - Section Key: `home_banner`
   - Language: `fr`
   - Title: `Nous Connectons. Vous Alimentez. Le Monde Fonctionne`
   - Subtitle: `Excellence du Réseau de Fibres`
   - Content: `Autonomiser les entreprises et les communautés avec une infrastructure de réseau de fibres de pointe.`

3. **About Section (English)**
   - Section Key: `home_services`
   - Language: `en`
   - Title: `COMPANY HISTORY`
   - Content: `Power Telco Business Limited (PTBL) is ECG's wholly owned subsidiary...`

### Quick Setup Commands

If you have the PocketBase MCP server available, you can use these commands to populate initial data:

```bash
# Create sample content sections
# (These would be executed via the MCP server)
```

## 🆘 Support

For issues specific to this implementation:
1. Check the browser console for JavaScript errors
2. Check PocketBase logs for server errors
3. Verify all environment variables are set correctly
4. Ensure all dependencies are installed

### Common Admin Tasks

- **Reset Admin Password**: Delete and recreate the admin user in PocketBase
- **Backup Content**: Export collections from PocketBase admin interface
- **Update Content**: Use the admin dashboard at `/edit-ptblgh`
- **Add New Languages**: Create new content sections with different language codes
